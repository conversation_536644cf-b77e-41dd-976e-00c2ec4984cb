# مشروع شاري (<PERSON><PERSON><PERSON>)

مشروع شاري هو تطبيق ويب للتسويق بالعمولة يتيح للمسوقين الترويج للمنتجات والحصول على عمولة عند بيعها.

## الهيكل العام للمشروع

```
Sharri/
├── src/
│   ├── components/          # المكونات القابلة لإعادة الاستخدام
│   │   ├── ui/             # مكونات واجهة المستخدم الأساسية
│   │   └── common/         # مكونات مشتركة
│   ├── pages/              # صفحات التطبيق
│   │   ├── Store.js        # صفحة المتجر
│   │   ├── Cart.js         # صفحة السلة
│   │   ├── Orders.js       # صفحة الطلبات
│   │   ├── Favorites.js    # صفحة المفضلة
│   │   ├── Account.js      # صفحة الحساب
│   │   └── NewOrder.js     # صفحة إنشاء طلب جديد
│   ├── entities/           # كيانات البيانات
│   │   ├── Product.js      # كيان المنتج
│   │   ├── CartItem.js     # كيان عنصر السلة
│   │   ├── Order.js        # كيان الطلب
│   │   ├── Favorite.js     # كيان المفضلة
│   │   └── Commission.js   # كيان العمولة
│   ├── api/                # واجهات برمجة التطبيقات
│   │   └── base44Client.js # عميل API
│   ├── utils/              # أدوات مساعدة
│   │   └── index.js        # وظائف مساعدة
│   ├── hooks/              # خطافات React مخصصة
│   ├── styles/             # ملفات الأنماط
│   └── assets/             # ملفات ثابتة (صور، أيقونات)
├── public/                 # ملفات عامة
├── package.json            # تبعيات المشروع
└── README.md              # هذا الملف
```

## كيفية تشغيل المشروع

1. تثبيت التبعيات:
```bash
npm install
```

2. تشغيل المشروع:
```bash
npm start
```

## التقنيات المستخدمة

- React.js
- React Router
- TanStack Query
- Tailwind CSS
- Lucide Icons
- Base44 API
