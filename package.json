{"name": "s<PERSON>ri", "version": "1.0.0", "description": "تطبيق شاري للتسويق بالعمولة", "main": "src/index.js", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "dependencies": {"@radix-ui/react-dropdown-menu": "^2.0.5", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-tabs": "^1.0.4", "@tanstack/react-query": "^4.42.0", "autoprefixer": "^10.4.14", "class-variance-authority": "^0.7.0", "clsx": "^1.2.1", "lucide-react": "^0.323.0", "postcss": "^8.4.21", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "react-scripts": "5.0.1", "tailwind-merge": "^1.10.0", "tailwindcss": "^3.2.7"}, "devDependencies": {"@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "typescript": "^4.9.4"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}