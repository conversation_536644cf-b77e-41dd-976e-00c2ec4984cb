const { spawn } = require('child_process');
const path = require('path');

// تشغيل npm install
console.log('Installing dependencies...');
const install = spawn('npm', ['install'], {
  cwd: path.resolve(__dirname),
  stdio: 'inherit',
  shell: true
});

install.on('close', (code) => {
  if (code !== 0) {
    console.error('Installation failed with code', code);
    return;
  }

  // تشغيل npm start
  console.log('Starting the application...');
  const start = spawn('npm', ['start'], {
    cwd: path.resolve(__dirname),
    stdio: 'inherit',
    shell: true
  });

  start.on('close', (code) => {
    console.log(`Application exited with code ${code}`);
  });
});
