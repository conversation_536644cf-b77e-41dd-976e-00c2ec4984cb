import React from 'react';
import { Routes, Route } from 'react-router-dom';
import Layout from './components/Layout';
import Store from './pages/Store';
import Cart from './pages/Cart';
import Orders from './pages/Orders';
import OrderDetails from './pages/OrderDetails';
import Favorites from './pages/Favorites';
import Account from './pages/Account';
import NewOrder from './pages/NewOrder';
import Categories from './pages/Categories';
import Notifications from './pages/Notifications';

function App() {
  return (
    <div className="App">
      <Routes future={{
        v7_startTransition: true,
        v7_relativeSplatPath: true
      }}>
      <Route path="/" element={<Layout><Store /></Layout>} />
      <Route path="/store" element={<Layout><Store /></Layout>} />
      <Route path="/categories" element={<Layout><Categories /></Layout>} />
      <Route path="/cart" element={<Layout><Cart /></Layout>} />
      <Route path="/orders" element={<Layout><Orders /></Layout>} />
      <Route path="/orders/:orderId" element={<Layout><OrderDetails /></Layout>} />
      <Route path="/favorites" element={<Layout><Favorites /></Layout>} />
      <Route path="/account" element={<Layout><Account /></Layout>} />
      <Route path="/neworder" element={<Layout><NewOrder /></Layout>} />
      <Route path="/notifications" element={<Layout><Notifications /></Layout>} />
      </Routes>
    </div>
  );
}

export default App;
