// Base44 API Client
// هذا الملف يحتوي على إعدادات الاتصال بـ Base44 API

// قم باستيراد مكتبة Base44 هنا
// import { Base44Client } from 'base44-sdk';

// تخزين مؤقت لعناصر السلة باستخدام localStorage
const getCartItemsFromStorage = () => {
  try {
    const items = localStorage.getItem('cartItems');
    return items ? JSON.parse(items) : [];
  } catch (error) {
    console.error('Error loading cart items from storage:', error);
    return [];
  }
};

const saveCartItemsToStorage = (items) => {
  try {
    localStorage.setItem('cartItems', JSON.stringify(items));
  } catch (error) {
    console.error('Error saving cart items to storage:', error);
  }
};

// تهيئة التخزين إذا كان فارغاً
if (!localStorage.getItem('cartItems')) {
  saveCartItemsToStorage([]);
}

const cartItemsStorage = getCartItemsFromStorage();

// تخزين مؤقت للطلبات باستخدام localStorage
const getOrdersFromStorage = () => {
  try {
    const orders = localStorage.getItem('orders');
    if (orders) {
      return JSON.parse(orders);
    } else {
      // تهيئة ببيانات وهمية إذا كان التخزين فارغاً
      const initialOrders = [
        {
          id: 'order1',
          status: 'تم التسليم',
          created_date: new Date('2023-10-15T14:30:00').toISOString(),
          marketer_email: '<EMAIL>',
          commission_amount: 10,
          customer_name: 'أحمد محمد',
          customer_phone: '01234567890',
          customer_address: 'القاهرة، مصر',
          total_price: 500,
          items_count: 3
        },
        {
          id: 'order2',
          status: 'قيد التوصيل',
          created_date: new Date('2023-11-02T10:15:00').toISOString(),
          marketer_email: '<EMAIL>',
          commission_amount: 15,
          customer_name: 'فاطمة علي',
          customer_phone: '01098765432',
          customer_address: 'الإسكندرية، مصر',
          total_price: 750,
          items_count: 5
        }
      ];
      saveOrdersToStorage(initialOrders);
      return initialOrders;
    }
  } catch (error) {
    console.error('Error loading orders from storage:', error);
    return [];
  }
};

const saveOrdersToStorage = (orders) => {
  try {
    localStorage.setItem('orders', JSON.stringify(orders));
  } catch (error) {
    console.error('Error saving orders to storage:', error);
  }
};

// تهيئة التخزين إذا كان فارغاً
if (!localStorage.getItem('orders')) {
  getOrdersFromStorage(); // سيتم تهيئة البيانات تلقائياً
}

// تخزين مؤقت لعناصر الطلب باستخدام localStorage
const getOrderItemsFromStorage = () => {
  try {
    const orderItems = localStorage.getItem('orderItems');
    if (orderItems) {
      return JSON.parse(orderItems);
    } else {
      // تهيئة ببيانات وهمية إذا كان التخزين فارغاً
      const initialOrderItems = [
        {
          id: 'item1',
          order_id: 'order1',
          product_id: 'prod1',
          product_name: 'منتج تجريبي 1',
          product_image: 'https://picsum.photos/seed/product1/300/300.jpg',
          price: 150,
          quantity: 1,
          commission_amount: 15
        },
        {
          id: 'item2',
          order_id: 'order1',
          product_id: 'prod2',
          product_name: 'منتج تجريبي 2',
          product_image: 'https://picsum.photos/seed/product2/300/300.jpg',
          price: 200,
          quantity: 1,
          commission_amount: 20
        },
        {
          id: 'item3',
          order_id: 'order1',
          product_id: 'prod3',
          product_name: 'منتج تجريبي 3',
          product_image: 'https://picsum.photos/seed/product3/300/300.jpg',
          price: 150,
          quantity: 1,
          commission_amount: 15
        },
        {
          id: 'item4',
          order_id: 'order2',
          product_id: 'prod4',
          product_name: 'منتج تجريبي 4',
          product_image: 'https://picsum.photos/seed/product4/300/300.jpg',
          price: 250,
          quantity: 2,
          commission_amount: 25
        },
        {
          id: 'item5',
          order_id: 'order2',
          product_id: 'prod5',
          product_name: 'منتج تجريبي 5',
          product_image: 'https://picsum.photos/seed/product5/300/300.jpg',
          price: 125,
          quantity: 1,
          commission_amount: 12.5
        },
        {
          id: 'item6',
          order_id: 'order2',
          product_id: 'prod6',
          product_name: 'منتج تجريبي 6',
          product_image: 'https://picsum.photos/seed/product6/300/300.jpg',
          price: 250,
          quantity: 2,
          commission_amount: 25
        }
      ];
      saveOrderItemsToStorage(initialOrderItems);
      return initialOrderItems;
    }
  } catch (error) {
    console.error('Error loading order items from storage:', error);
    return [];
  }
};

const saveOrderItemsToStorage = (orderItems) => {
  try {
    localStorage.setItem('orderItems', JSON.stringify(orderItems));
  } catch (error) {
    console.error('Error saving order items to storage:', error);
  }
};

// تهيئة التخزين إذا كان فارغاً
if (!localStorage.getItem('orderItems')) {
  getOrderItemsFromStorage(); // سيتم تهيئة البيانات تلقائياً
}

// إنشاء عميل API
const base44 = {
  // المصادقة
  auth: {
    // تسجيل الدخول
    signIn: async (email, password) => {
      // تنفيذ عملية تسجيل الدخول
      // const response = await Base44Client.signIn(email, password);
      // return response;

      // مؤقتًا: قيمة وهمية
      return new Promise(resolve => {
        setTimeout(() => {
          resolve({
            id: 'user123',
            email,
            full_name: 'مستخدم تجريبي'
          });
        }, 500);
      });
    },

    // الحصول على معلومات المستخدم الحالي
    me: async () => {
      // تنفيذ عملية الحصول على معلومات المستخدم
      // const response = await Base44Client.auth.me();
      // return response;

      // مؤقتًا: قيمة وهمية
      return new Promise(resolve => {
        setTimeout(() => {
          resolve({
            id: 'user123',
            email: '<EMAIL>',
            full_name: 'مستخدم تجريبي'
          });
        }, 500);
      });
    },

    // تسجيل الخروج
    signOut: async () => {
      // تنفيذ عملية تسجيل الخروج
      // await Base44Client.auth.signOut();

      // مؤقتًا: قيمة وهمية
      return new Promise(resolve => {
        setTimeout(() => {
          resolve();
        }, 500);
      });
    }
  },

  // الكيانات (البيانات)
  entities: {
    // المنتجات
    Product: {
      filter: async (filters, orderBy) => {
        // تنفيذ عملية التصفية
        // const response = await Base44Client.entities.Product.filter(filters, orderBy);
        // return response;

        // مؤقتًا: قيمة وهمية
        return new Promise(resolve => {
          setTimeout(() => {
            resolve([
              {
                id: 'prod1',
                name: 'منتج تجريبي 1',
                price: 100,
                commission_amount: 10,
                category: 'رجالي',
                image_url: 'https://picsum.photos/seed/product/300/300.jpg',
                is_active: true
              },
              {
                id: 'prod2',
                name: 'منتج تجريبي 2',
                price: 200,
                commission_amount: 20,
                category: 'حريمي',
                image_url: 'https://picsum.photos/seed/product/300/300.jpg',
                is_active: true
              }
            ]);
          }, 500);
        });
      },

      get: async (id) => {
        // تنفيذ عملية الحصول على منتج
        // const response = await Base44Client.entities.Product.get(id);
        // return response;

        // مؤقتًا: قيمة وهمية
        return new Promise(resolve => {
          setTimeout(() => {
            resolve({
              id,
              name: 'منتج تجريبي',
              price: 100,
              commission_amount: 10,
              category: 'رجالي',
              image_url: 'https://picsum.photos/seed/product/300/300.jpg',
              is_active: true
            });
          }, 500);
        });
      }
    },

    // عناصر السلة
    CartItem: {
      filter: async (filters, orderBy) => {
        // مؤقتًا: قيمة وهمية مع الفلترة حسب البريد الإلكتروني
        return new Promise(resolve => {
          setTimeout(() => {
            // تحديث البيانات من localStorage
            let items = [...getCartItemsFromStorage()];
            
            // تطبيق الفلتر حسب البريد الإلكتروني
            if (filters && filters.marketer_email) {
              items = items.filter(item => item.marketer_email === filters.marketer_email);
            }
            
            resolve(items);
          }, 500);
        });
      },

      create: async (data) => {
        // مؤقتًا: قيمة وهمية مع إضافة حقيقية للعنصر
        return new Promise(resolve => {
          setTimeout(() => {
            const newItem = {
              id: `cart_${Date.now()}`,
              ...data
            };
            
            // الحصول على البيانات الحالية من localStorage
            const currentItems = getCartItemsFromStorage();
            
            // إضافة العنصر الجديد
            currentItems.push(newItem);
            
            // حفظ البيانات المحدثة في localStorage
            saveCartItemsToStorage(currentItems);
            
            resolve(newItem);
          }, 500);
        });
      },

      update: async (id, data) => {
        // مؤقتًا: قيمة وهمية مع التحديث الفعلي
        return new Promise(resolve => {
          setTimeout(() => {
            // الحصول على البيانات الحالية من localStorage
            const currentItems = getCartItemsFromStorage();
            
            // البحث عن العنصر وتحديثه
            const index = currentItems.findIndex(item => item.id === id);
            if (index !== -1) {
              currentItems[index] = { ...currentItems[index], ...data };
              
              // حفظ البيانات المحدثة في localStorage
              saveCartItemsToStorage(currentItems);
              
              resolve(currentItems[index]);
            } else {
              resolve({ id, ...data });
            }
          }, 500);
        });
      },

      delete: async (id) => {
        // مؤقتًا: قيمة وهمية مع الحذف الفعلي
        return new Promise(resolve => {
          setTimeout(() => {
            // الحصول على البيانات الحالية من localStorage
            const currentItems = getCartItemsFromStorage();
            
            // البحث عن العنصر وحذفه
            const index = currentItems.findIndex(item => item.id === id);
            if (index !== -1) {
              currentItems.splice(index, 1);
              
              // حفظ البيانات المحدثة في localStorage
              saveCartItemsToStorage(currentItems);
            }
            resolve();
          }, 500);
        });
      }
    },

    // الطلبات
    Order: {
      filter: async (filters, orderBy) => {
        return new Promise(resolve => {
          setTimeout(() => {
            let orders = getOrdersFromStorage();

            // تطبيق الفلاتر
            if (filters) {
              if (filters.marketer_email) {
                orders = orders.filter(order => order.marketer_email === filters.marketer_email);
              }
            }

            // تطبيق الترتيب
            if (orderBy) {
              if (orderBy === '-created_date') {
                orders = orders.sort((a, b) => new Date(b.created_date) - new Date(a.created_date));
              } else if (orderBy === 'created_date') {
                orders = orders.sort((a, b) => new Date(a.created_date) - new Date(b.created_date));
              }
            }

            resolve(orders);
          }, 500);
        });
      },

      get: async (id) => {
        // مؤقتًا: قيمة وهمية
        return new Promise(resolve => {
          setTimeout(() => {
            if (id === 'order1') {
              resolve({
                id: 'order1',
                status: 'تم التسليم',
                created_date: new Date('2023-10-15T14:30:00').toISOString(),
                marketer_email: '<EMAIL>',
                commission_amount: 10,
                customer_name: 'أحمد محمد',
                customer_phone: '01234567890',
                customer_address: 'القاهرة، مصر',
                total_price: 500,
                items_count: 3
              });
            } else if (id === 'order2') {
              resolve({
                id: 'order2',
                status: 'قيد التوصيل',
                created_date: new Date('2023-11-02T10:15:00').toISOString(),
                marketer_email: '<EMAIL>',
                commission_amount: 15,
                customer_name: 'فاطمة علي',
                customer_phone: '01098765432',
                customer_address: 'الإسكندرية، مصر',
                total_price: 750,
                items_count: 5
              });
            } else {
              // بيانات وهمية لأي طلب آخر
              resolve({
                id,
                status: 'قيد المعالجة',
                created_date: new Date('2023-11-05T16:45:00').toISOString(),
                marketer_email: '<EMAIL>',
                commission_amount: 5,
                customer_name: 'عميل تجريبي',
                customer_phone: '01122334455',
                customer_address: 'القاهرة، مصر',
                total_price: 300,
                items_count: 2
              });
            }
          }, 500);
        });
      },

      create: async (data) => {
        return new Promise(resolve => {
          setTimeout(() => {
            // الحصول على الطلبات الحالية
            const orders = getOrdersFromStorage();

            // إنشاء طلب جديد
            const orderId = 'order_' + Date.now();
            const newOrder = {
              id: orderId,
              ...data,
              status: 'قيد المعالجة',
              created_date: new Date().toISOString()
            };

            // إضافة الطلب الجديد إلى القائمة
            orders.push(newOrder);

            // حفظ القائمة المحدثة
            saveOrdersToStorage(orders);

            // حفظ عناصر الطلب بشكل منفصل
            if (data.items && data.items.length > 0) {
              const orderItems = getOrderItemsFromStorage();
              const newOrderItems = data.items.map((item, index) => ({
                id: 'item_' + orderId + '_' + index,
                order_id: orderId,
                product_id: item.product_id,
                product_name: item.product_name,
                price: item.product_price,
                quantity: item.quantity,
                commission_amount: item.commission_amount
              }));

              // إضافة العناصر الجديدة
              orderItems.push(...newOrderItems);

              // حفظ القائمة المحدثة
              saveOrderItemsToStorage(orderItems);
            }

            resolve(newOrder);
          }, 500);
        });
      }
    },

    // عناصر الطلب
    OrderItem: {
      filter: async (filters, orderBy) => {
        return new Promise(resolve => {
          setTimeout(() => {
            let orderItems = getOrderItemsFromStorage();

            // تطبيق الفلاتر
            if (filters) {
              if (filters.order_id) {
                orderItems = orderItems.filter(item => item.order_id === filters.order_id);
              }
            }

            // تطبيق الترتيب
            if (orderBy) {
              if (orderBy === '-created_date') {
                orderItems = orderItems.sort((a, b) => new Date(b.created_date) - new Date(a.created_date));
              } else if (orderBy === 'created_date') {
                orderItems = orderItems.sort((a, b) => new Date(a.created_date) - new Date(b.created_date));
              }
            }

            resolve(orderItems);
          }, 500);
        });
      }
    },

    // المفضلة
    Favorite: {
      filter: async (filters, orderBy) => {
        // مؤقتًا: قيمة وهمية
        return new Promise(resolve => {
          setTimeout(() => {
            resolve([
              {
                id: 'fav1',
                product_id: 'prod1',
                product_name: 'منتج تجريبي 1',
                product_price: 100,
                commission_amount: 10,
                product_image: 'https://picsum.photos/seed/cart/300/300.jpg'
              }
            ]);
          }, 500);
        });
      },

      create: async (data) => {
        // مؤقتًا: قيمة وهمية
        return new Promise(resolve => {
          setTimeout(() => {
            resolve({
              id: 'fav_new',
              ...data
            });
          }, 500);
        });
      },

      delete: async (id) => {
        // مؤقتًا: قيمة وهمية
        return new Promise(resolve => {
          setTimeout(() => {
            resolve();
          }, 500);
        });
      }
    },

    // العمولات
    Commission: {
      filter: async (filters, orderBy) => {
        // مؤقتًا: قيمة وهمية
        return new Promise(resolve => {
          setTimeout(() => {
            resolve([
              {
                id: 'comm1',
                order_id: 'order1',
                amount: 10,
                status: 'مدفوعة',
                created_date: new Date().toISOString()
              }
            ]);
          }, 500);
        });
      }
    }
  }
};

export default base44;
