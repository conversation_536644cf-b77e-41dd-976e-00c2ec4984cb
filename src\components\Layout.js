import React, { useState, useEffect } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { createPageUrl } from "../utils";
import base44 from "../api/base44Client";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useNotifications } from "../hooks/useNotifications";
import WhatsAppIcon from "./WhatsAppIcon";
import {
  ShoppingCart,
  Store,
  Bell,
  User,
  Heart,
  MessageCircle,

  Search,
  ShoppingBag,
  Filter,
  Grid3X3
} from "lucide-react";
import { Badge } from "./ui/Badge";
import { Input } from "./ui/Input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "./ui/DropdownMenu";

const navigationItems = [
  {
    title: "المتجر",
    url: createPageUrl("Store"),
    icon: Store,
  },
  {
    title: "الفئات",
    url: createPageUrl("Categories"),
    icon: Grid3X3,
  },
  {
    title: "الطلبات",
    url: createPageUrl("Orders"),
    icon: ShoppingCart,
  },
  {
    title: "المفضلة",
    url: createPageUrl("Favorites"),
    icon: Heart,
  },

];

export default function Layout({ children }) {
  const location = useLocation();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  const [searchQuery, setSearchQuery] = useState("");
  // استخدام هوك الإشعارات
  const { notificationsCount } = useNotifications();
  
  // دالة لإظهار الإشعارات
  const [showNotification, setShowNotification] = useState(false);
  
  // دالة لعرض إشارة المنتج المضافة
  const showAddedToCartNotification = () => {
    setShowNotification(true);
    setTimeout(() => setShowNotification(false), 1000);
  };

  useEffect(() => {
    loadUser();
  }, []);

  const loadUser = async () => {
    try {
      const currentUser = await base44.auth.me();
      setUser(currentUser);
    } catch (error) {
      console.error("Error loading user:", error);
    } finally {
      setLoading(false);
    }
  };



  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`${createPageUrl("Store")}?search=${searchQuery}`);
    }
  };
  
  const handleSearchChange = (value) => {
    setSearchQuery(value);
    if (value.trim()) {
      navigate(`${createPageUrl("Store")}?search=${value}`);
    } else {
      navigate(createPageUrl("Store"));
    }
  };

  // استعلام لجلب عناصر السلة
  const { data: cartItems = [] } = useQuery({
    queryKey: ['cart'],
    queryFn: () => base44.entities.CartItem.filter({ marketer_email: user?.email }, '-created_date'),
    enabled: !!user?.email,
  });

  // استعلام لجلب عناصر المفضلة
  const { data: favorites = [] } = useQuery({
    queryKey: ['favorites'],
    queryFn: () => base44.entities.Favorite.filter({ marketer_email: user?.email }),
    enabled: !!user?.email,
  });

  if (loading) {
    return (
      <div className={`min-h-screen flex items-center justify-center ${'bg-gradient-to-br from-yellow-50 to-yellow-100'}`}>
        <div className={`animate-spin rounded-full h-12 w-12 border-b-2 ${'border-yellow-500'}`}></div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen pb-12 sm:pb-16 md:pb-20 transition-colors duration-300 ${'bg-gradient-to-br from-yellow-50 via-yellow-100 to-amber-50'}`}>
      <style>{`
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700;800;900&display=swap');

        :root {
          --primary: #FFF100;
          --primary-dark: #FFE600;
          --primary-light: #FFEB3B;
          --background: #FFFDE7;
          --text-primary: #1A1A1A;
          --text-secondary: #424242;
          --accent: #000000;
        }
        * {
          direction: rtl;
          font-family: 'Cairo', 'Segoe UI', sans-serif;
          box-sizing: border-box;
        }
        
        /* تحسينات للأجهزة الصغيرة جداً (iPhone 4/4S) */
        @media (max-width: 320px) {
          body {
            font-size: 12px;
          }
          .container {
            padding: 8px;
          }
          .top-bar {
            padding: 8px;
          }
          .logo {
            width: 32px;
            height: 32px;
          }
          .logo-text {
            font-size: 16px;
          }
          .search-bar {
            height: 32px;
            font-size: 12px;
          }
          .nav-icon {
            width: 32px;
            height: 32px;
            padding: 6px;
          }
          .bottom-nav {
            padding: 4px 0;
          }
          .nav-item {
            padding: 4px 6px;
            font-size: 10px;
          }
          .nav-icon-small {
            width: 16px;
            height: 16px;
          }
          .badge {
            width: 16px;
            height: 16px;
            font-size: 8px;
          }
        }
        
        /* تحسينات للأجهزة الصغيرة (iPhone 5/5S/SE) */
        @media (max-width: 375px) {
          body {
            font-size: 14px;
          }
          .container {
            padding: 12px;
          }
          .top-bar {
            padding: 12px;
          }
          .logo {
            width: 36px;
            height: 36px;
          }
          .logo-text {
            font-size: 18px;
          }
          .search-bar {
            height: 36px;
            font-size: 14px;
          }
          .nav-icon {
            width: 36px;
            height: 36px;
            padding: 8px;
          }
          .bottom-nav {
            padding: 6px 0;
          }
          .nav-item {
            padding: 6px 8px;
            font-size: 11px;
          }
          .nav-icon-small {
            width: 18px;
            height: 18px;
          }
          .badge {
            width: 18px;
            height: 18px;
            font-size: 9px;
          }
        }
        
        /* تحسينات للأجهزة متوسطة الحجم (iPhone 6/6S/7/8) */
        @media (max-width: 414px) {
          body {
            font-size: 15px;
          }
          .container {
            padding: 14px;
          }
          .top-bar {
            padding: 14px;
          }
          .logo {
            width: 40px;
            height: 40px;
          }
          .logo-text {
            font-size: 20px;
          }
          .search-bar {
            height: 40px;
            font-size: 15px;
          }
          .nav-icon {
            width: 40px;
            height: 40px;
            padding: 10px;
          }
          .bottom-nav {
            padding: 8px 0;
          }
          .nav-item {
            padding: 8px 10px;
            font-size: 12px;
          }
          .nav-icon-small {
            width: 20px;
            height: 20px;
          }
          .badge {
            width: 20px;
            height: 20px;
            font-size: 10px;
          }
        }
        
        /* تحسينات للأجهزة متوسطة الحجم (iPhone 6 Plus/7 Plus/8 Plus) */
        @media (max-width: 736px) {
          body {
            font-size: 16px;
          }
          .container {
            padding: 16px;
          }
          .top-bar {
            padding: 16px;
          }
          .logo {
            width: 44px;
            height: 44px;
          }
          .logo-text {
            font-size: 22px;
          }
          .search-bar {
            height: 44px;
            font-size: 16px;
          }
          .nav-icon {
            width: 44px;
            height: 44px;
            padding: 12px;
          }
          .bottom-nav {
            padding: 10px 0;
          }
          .nav-item {
            padding: 10px 12px;
            font-size: 13px;
          }
          .nav-icon-small {
            width: 22px;
            height: 22px;
          }
          .badge {
            width: 22px;
            height: 22px;
            font-size: 11px;
          }
        }
        
        /* تحسينات للأجهزة الكبيرة (iPhone X/XR/11/12/13/14/15) */
        @media (min-width: 375px) and (max-width: 428px) {
          body {
            font-size: 17px;
          }
          .container {
            padding: 18px;
          }
          .top-bar {
            padding: 18px;
          }
          .logo {
            width: 48px;
            height: 48px;
          }
          .logo-text {
            font-size: 24px;
          }
          .search-bar {
            height: 48px;
            font-size: 17px;
          }
          .nav-icon {
            width: 48px;
            height: 48px;
            padding: 14px;
          }
          .bottom-nav {
            padding: 12px 0;
          }
          .nav-item {
            padding: 12px 14px;
            font-size: 14px;
          }
          .nav-icon-small {
            width: 24px;
            height: 24px;
          }
          .badge {
            width: 24px;
            height: 24px;
            font-size: 12px;
          }
        }
        
        /* تحسينات للأجهزة اللوحية (iPad) */
        @media (min-width: 768px) and (max-width: 1024px) {
          body {
            font-size: 18px;
          }
          .container {
            padding: 20px;
          }
          .top-bar {
            padding: 20px;
          }
          .logo {
            width: 52px;
            height: 52px;
          }
          .logo-text {
            font-size: 26px;
          }
          .search-bar {
            height: 52px;
            font-size: 18px;
          }
          .nav-icon {
            width: 52px;
            height: 52px;
            padding: 16px;
          }
          .bottom-nav {
            padding: 14px 0;
          }
          .nav-item {
            padding: 14px 16px;
            font-size: 15px;
          }
          .nav-icon-small {
            width: 26px;
            height: 26px;
          }
          .badge {
            width: 26px;
            height: 26px;
            font-size: 13px;
          }
          .grid-cols-3 {
            grid-template-columns: repeat(3, minmax(0, 1fr));
          }
        }
        
        /* تحسينات للأجهزة اللوحية الكبيرة (iPad Pro) */
        @media (min-width: 1024px) {
          body {
            font-size: 19px;
          }
          .container {
            padding: 24px;
          }
          .top-bar {
            padding: 24px;
          }
          .logo {
            width: 56px;
            height: 56px;
          }
          .logo-text {
            font-size: 28px;
          }
          .search-bar {
            height: 56px;
            font-size: 19px;
          }
          .nav-icon {
            width: 56px;
            height: 56px;
            padding: 18px;
          }
          .bottom-nav {
            padding: 16px 0;
          }
          .nav-item {
            padding: 16px 18px;
            font-size: 16px;
          }
          .nav-icon-small {
            width: 28px;
            height: 28px;
          }
          .badge {
            width: 28px;
            height: 28px;
            font-size: 14px;
          }
          .grid-cols-4 {
            grid-template-columns: repeat(4, minmax(0, 1fr));
          }
        }
        
        /* تحسينات للأجهزة اللوحية الكبيرة جداً */
        @media (min-width: 1280px) {
          .grid-cols-5 {
            grid-template-columns: repeat(5, minmax(0, 1fr));
          }
        }
        
        /* تحسينات عامة */
        * {
          box-sizing: border-box;
        }
        
        body {
          overflow-x: hidden;
        }
        
        img {
          max-width: 100%;
          height: auto;
        }
        
        button {
          cursor: pointer;
          touch-action: manipulation;
        }
        
        input, textarea, select {
          font-size: 16px; /* منع التكبير في iOS */
        }
        
        /* تحسينات للأجهزة التي تعمل باللمس */
        @media (hover: none) {
          button:hover {
            background-color: inherit;
          }
          
          a:hover {
            color: inherit;
          }
        }
      `}</style>

      {/* Top Bar */}
      <div className={`fixed top-0 left-0 right-0 z-50 transition-colors duration-300 ${'bg-white border-yellow-200'} border-b-2 shadow-lg`}>
        <div className="px-4 py-3">
          {/* First Row: Logo + Search + Icons */}
          <div className="flex items-center gap-3 mb-3">
            {/* Logo */}
            <div className="flex items-center gap-2">
              <div
                onClick={() => navigate(createPageUrl("Store"))}
                className={`w-12 h-12 rounded-2xl flex items-center justify-center shadow-xl border-2 cursor-pointer transition-all hover:scale-105 ${'bg-black border-yellow-500'}`}
              >
                <img
                  src="https://qtrypzzcjebvfcihiynt.supabase.co/storage/v1/object/public/base44-prod/public/69046ca9f20b16652ad370f9/da9a104c3_RED.png"
                  alt="Logo"
                  className="w-10 h-10 object-contain"
                />
              </div>
              <h2 className={`text-xl md:text-2xl font-bold ${'text-black'}`} style={{ fontFamily: 'Cairo, sans-serif' }}>شــاري</h2>
            </div>

            {/* Search Bar */}
            <form onSubmit={handleSearch} className="flex-1 max-w-sm md:max-w-md lg:max-w-lg mx-auto">
              <div className="relative">
                <Search className={`absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 ${'text-gray-500'}`} />
                <Input
                  value={searchQuery}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  placeholder="بحث..."
                  className={`pr-9 pl-3 py-1.5 text-xs md:text-sm rounded-full border-2 font-semibold w-full ${'bg-gray-50 border-gray-300 text-gray-900'}`}
                />
              </div>
            </form>

            {/* Icons Group */}
            <div className="flex items-center gap-2">
              {/* Cart */}
              <Link
                to={createPageUrl("Cart")}
                className={`relative p-2 rounded-xl transition-colors ${'bg-yellow-100 hover:bg-yellow-200'}`}
              >
                <ShoppingCart className={`w-5 h-5 ${'text-gray-700'}`} />
                {cartItems.length > 0 && (
                  <span className="absolute top-1 right-1 bg-black text-white text-xs rounded-full w-4 h-4 flex items-center justify-center text-[10px] font-bold animate-pulse">
                    {cartItems.length > 99 ? "99+" : cartItems.length}
                  </span>
                )}
              </Link>
              


              {/* Notifications */}
              <Link to={createPageUrl("Notifications")} className={`relative p-2 rounded-xl transition-colors ${'bg-yellow-100 hover:bg-yellow-200'}`}>
                <Bell className={`w-5 h-5 ${'text-gray-700'}`} />
                {notificationsCount > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold animate-pulse">
                    {notificationsCount > 99 ? "99+" : notificationsCount}
                  </span>
                )}
              </Link>
              
              {/* Account */}
              <Link to={createPageUrl("Account")} className={`relative p-1 transition-colors ${'bg-yellow-100 hover:bg-yellow-200 rounded-full'}`}>
                <div className="w-8 h-8 rounded-full overflow-hidden border-2 border-yellow-400 flex items-center justify-center bg-black">
                  {user?.image_url ? (
                    <img 
                      src={user.image_url} 
                      alt="حسابي" 
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <User className={`w-5 h-5 ${'text-white'}`} />
                  )}
                </div>
              </Link>




            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="pt-20">
        {children}
      </div>
      
      {/* Notification Toast - Center Screen */}
      {showNotification && (
        <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 animate-fadeInOut">
          <div className="flex items-center gap-2">
            <ShoppingCart className="w-5 h-5" />
            <span>تم إضافة المنتج إلى السلة بنجاح</span>
          </div>
        </div>
      )}

      {/* WhatsApp Icon - Show on all pages except Orders */}
      {location.pathname !== createPageUrl("Orders") && !location.pathname.startsWith(createPageUrl("Orders") + "/") && (
        <WhatsAppIcon 
          phoneNumber="+201234567890" 
          message="مرحباً، أود الاستفسار عن نظام العمل" 
        />
      )}

      {/* Bottom Navigation */}
      <div className={`fixed bottom-0 left-0 right-0 z-50 transition-colors duration-300 ${'bg-white border-yellow-200'} border-t-2 shadow-2xl`}>
        <div className="flex items-center justify-around px-1 py-2">
          {navigationItems.map((item) => {
            const isActive = location.pathname === item.url;
            return (
              <Link
                key={item.title}
                to={item.url}
                className={`flex flex-col items-center gap-1 px-3 py-2 rounded-2xl transition-all duration-300 relative ${
                  isActive
                    ? 'bg-black text-yellow-400 shadow-2xl scale-105'
                    : 'text-gray-900 hover:bg-yellow-100'
                }`}
              >
                <div className="relative">
                  <item.icon className="w-5 h-5" />
                  {item.title === "السلة" && cartItems.length > 0 && (
                    <span className="absolute -top-2 -right-2 bg-black text-white text-xs rounded-full w-5 h-5 flex items-center justify-center animate-pulse">
                      {cartItems.length > 99 ? "99+" : cartItems.length}
                    </span>
                  )}
                  {item.title === "المفضلة" && favorites.length > 0 && (
                    <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center animate-pulse">
                      {favorites.length > 99 ? "99+" : favorites.length}
                    </span>
                  )}
                </div>
                <span className="text-[10px] font-bold" style={{ fontFamily: 'Cairo, sans-serif' }}>
                  {item.title}
                </span>
              </Link>
            );
          })}
        </div>
      </div>
    </div>
  );
}
