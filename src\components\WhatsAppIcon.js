
import React from 'react';
import { MessageCircle } from 'lucide-react';

export default function WhatsAppIcon({ phoneNumber, message, className, showInOrdersPage = false }) {
  // تحويل رقم الهاتف إلى الرابط المناسب
  const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;

  // إذا كنا في صفحة الطلبات، نعرض الأيقونة الصغيرة
  if (showInOrdersPage) {
    return (
      <a 
        href={whatsappUrl} 
        target="_blank" 
        rel="noopener noreferrer"
        className={`inline-flex items-center justify-center p-1.5 rounded-lg bg-green-500 hover:bg-green-600 text-white shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105 active:scale-95 ${className}`}
      >
        <MessageCircle className="w-4 h-4" />
      </a>
    );
  }

  // في الصفحات الأخرى، نعرض الأيقونة الثابتة الكبيرة
  return (
    <a 
      href={whatsappUrl} 
      target="_blank" 
      rel="noopener noreferrer"
      className={`fixed bottom-24 right-4 z-50 flex items-center justify-center w-14 h-14 bg-green-500 hover:bg-green-600 rounded-full shadow-lg text-white transition-all hover:scale-110 ${className}`}
    >
      <MessageCircle className="w-7 h-7" />
    </a>
  );
}
