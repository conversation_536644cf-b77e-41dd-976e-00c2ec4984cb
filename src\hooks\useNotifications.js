import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useEffect } from "react";

// دالة للحصول على الإشعارات من التخزين المحلي
const getStoredNotifications = () => {
  try {
    const stored = localStorage.getItem('notifications');
    return stored ? JSON.parse(stored) : null;
  } catch (error) {
    console.error("Error loading notifications from localStorage:", error);
    return null;
  }
};

// دالة لحفظ الإشعارات في التخزين المحلي
const saveNotificationsToStorage = (notifications) => {
  try {
    localStorage.setItem('notifications', JSON.stringify(notifications));
  } catch (error) {
    console.error("Error saving notifications to localStorage:", error);
  }
};

// هذا الهوك يدير نظام الإشعارات في التطبيق
export function useNotifications() {
  const queryClient = useQueryClient();

  // جلب الإشعارات من التخزين المحلي أو الخادم
  const { data: notifications = [], isLoading } = useQuery({
    queryKey: ["notifications"],
    queryFn: async () => {
      // أولاً، حاول جلب الإشعارات من التخزين المحلي
      const storedNotifications = getStoredNotifications();
      if (storedNotifications) {
        return storedNotifications;
      }

      // إذا لم توجد إشعارات في التخزين المحلي، استخدم البيانات الافتراضية
      const defaultNotifications = [
        {
          id: 1,
          type: "new_product",
          title: "منتج جديد متاح",
          message: "تم إضافة منتج جديد للمتجر",
          time: "منذ 5 دقائق",
          read: false,
          data: { productId: "123" }
        },
        {
          id: 2,
          type: "low_stock",
          title: "نفاذ كمية",
          message: "المنتج X على وشك النفاذ",
          time: "منذ 30 دقيقة",
          read: false,
          data: { productId: "456" }
        },
        {
          id: 3,
          type: "order_status",
          title: "تغيير حالة الطلب",
          message: "تم تغيير حالة طلبك #12345 إلى قيد الشحن",
          time: "منذ ساعة",
          read: true,
          data: { orderId: "12345" }
        }
      ];

      // حفظ البيانات الافتراضية في التخزين المحلي
      saveNotificationsToStorage(defaultNotifications);
      return defaultNotifications;
    }
  });

  // جلب عدد الإشعارات غير المقروءة
  const { data: notificationsCount = 0 } = useQuery({
    queryKey: ["notificationsCount"],
    queryFn: async () => {
      // في التطبيق الحقيقي، سيتم جلب العدد من API
      // هنا نقوم بحساب العدد من الإشعارات المحلية
      return notifications.filter(n => !n.read).length;
    },
    initialData: 0
  });

  // تحديث حالة الإشعار كمقروء
  const markAsReadMutation = useMutation({
    mutationFn: async (id) => {
      // في التطبيق الحقيقي، سيتم إرسال طلب API لتحديث حالة الإشعار
      return new Promise(resolve => setTimeout(() => resolve(id), 300));
    },
    onSuccess: (id) => {
      // تحديث البيانات المحلية وحفظها في التخزين المحلي
      queryClient.setQueryData(["notifications"], (oldNotifications) => {
        const updatedNotifications = oldNotifications.map(notification =>
          notification.id === id ? { ...notification, read: true } : notification
        );
        // حفظ التغييرات في التخزين المحلي
        saveNotificationsToStorage(updatedNotifications);
        return updatedNotifications;
      });
      // تحديث عدد الإشعارات
      queryClient.invalidateQueries(["notificationsCount"]);
    }
  });

  // حذف إشعار
  const deleteNotificationMutation = useMutation({
    mutationFn: async (id) => {
      // في التطبيق الحقيقي، سيتم إرسال طلب API لحذف الإشعار
      return new Promise(resolve => setTimeout(() => resolve(id), 300));
    },
    onSuccess: (id) => {
      // تحديث البيانات المحلية وحفظها في التخزين المحلي
      queryClient.setQueryData(["notifications"], (oldNotifications) => {
        const updatedNotifications = oldNotifications.filter(notification => notification.id !== id);
        // حفظ التغييرات في التخزين المحلي
        saveNotificationsToStorage(updatedNotifications);
        return updatedNotifications;
      });
      // تحديث عدد الإشعارات
      queryClient.invalidateQueries(["notificationsCount"]);
    }
  });

  // تحديث كل الإشعارات كمقروءة
  const markAllAsReadMutation = useMutation({
    mutationFn: async () => {
      // في التطبيق الحقيقي، سيتم إرسال طلب API لتحديث كل الإشعارات
      return new Promise(resolve => setTimeout(() => resolve(), 300));
    },
    onSuccess: () => {
      // تحديث البيانات المحلية وحفظها في التخزين المحلي
      queryClient.setQueryData(["notifications"], (oldNotifications) => {
        const updatedNotifications = oldNotifications.map(notification => ({ ...notification, read: true }));
        // حفظ التغييرات في التخزين المحلي
        saveNotificationsToStorage(updatedNotifications);
        return updatedNotifications;
      });
      // تحديث عدد الإشعارات
      queryClient.invalidateQueries(["notificationsCount"]);
    }
  });

  // إضافة إشعار جديد
  const addNotificationMutation = useMutation({
    mutationFn: async (notification) => {
      // في التطبيق الحقيقي، سيتم إرسال طلب API لإضافة إشعار
      return new Promise(resolve => setTimeout(() => resolve(notification), 300));
    },
    onSuccess: (newNotification) => {
      // تحديث البيانات المحلية وحفظها في التخزين المحلي
      queryClient.setQueryData(["notifications"], (oldNotifications) => {
        const updatedNotifications = [newNotification, ...oldNotifications];
        // حفظ التغييرات في التخزين المحلي
        saveNotificationsToStorage(updatedNotifications);
        return updatedNotifications;
      });
      // تحديث عدد الإشعارات
      queryClient.invalidateQueries(["notificationsCount"]);
    }
  });

  // دوال مساعدة
  const markAsRead = (id) => markAsReadMutation.mutate(id);
  const deleteNotification = (id) => deleteNotificationMutation.mutate(id);
  const markAllAsRead = () => markAllAsReadMutation.mutate();
  const addNotification = (notification) => addNotificationMutation.mutate(notification);

  return {
    notifications,
    notificationsCount,
    isLoading,
    markAsRead,
    deleteNotification,
    markAllAsRead,
    addNotification
  };
}

// دالة مساعدة لإضافة أنواع مختلفة من الإشعارات
export const useNotificationActions = () => {
  const { addNotification } = useNotifications();
  const queryClient = useQueryClient();

  // إضافة إشعار عند إضافة منتج جديد
  const notifyNewProduct = (product) => {
    addNotification({
      id: Date.now(),
      type: "new_product",
      title: "منتج جديد متاح",
      message: `تم إضافة منتج ${product.name} للمتجر`,
      time: "الآن",
      read: false,
      data: { productId: product.id }
    });
  };

  // إضافة إشعار عند نفاذ الكمية
  const notifyLowStock = (product) => {
    addNotification({
      id: Date.now(),
      type: "low_stock",
      title: "نفاذ كمية",
      message: `المنتج ${product.name} على وشك النفاذ`,
      time: "الآن",
      read: false,
      data: { productId: product.id }
    });
  };

  // إضافة إشعار عند تغيير حالة الطلب
  const notifyOrderStatusChange = (order, newStatus) => {
    addNotification({
      id: Date.now(),
      type: "order_status",
      title: "تغيير حالة الطلب",
      message: `تم تغيير حالة طلبك #${order.id} إلى ${newStatus}`,
      time: "الآن",
      read: false,
      data: { orderId: order.id }
    });
  };

  return {
    notifyNewProduct,
    notifyLowStock,
    notifyOrderStatusChange
  };
};
