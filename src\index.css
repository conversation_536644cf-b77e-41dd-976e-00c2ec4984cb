@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary: #FFF100;
  --primary-dark: #FFE600;
  --primary-light: #FFEB3B;
  --background: #FFFDE7;
  --text-primary: #1A1A1A;
  --text-secondary: #424242;
  --accent: #000000;
}

* {
  direction: rtl;
  font-family: 'Cairo', 'Segoe UI', sans-serif;
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Cairo', 'Segoe UI', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--background);
  color: #000000;
  overflow-x: hidden;
  min-height: 100vh;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* شريط البحث الثابت في الأعلى */
.search-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: var(--primary);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  padding: 12px 15px;
}

.search-container {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 20px;
  padding: 8px 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  background: none;
  margin-right: 10px;
  font-family: 'Cairo', 'Segoe UI', sans-serif;
  font-size: 14px;
}

.search-icon {
  color: var(--text-secondary);
  font-size: 18px;
}

/* منطقة المحتوى الرئيسية */
.main-content {
  padding-top: 70px; /* ارتفاع شريط البحث */
  padding-bottom: 70px; /* ارتفاع قائمة الأيقونات */
  overflow-y: auto;
  width: 100%;
  padding-right: 10px;
  padding-left: 10px;
}

/* قائمة الأيقونات الثابتة في الأسفل */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: var(--primary);
  box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);
  padding: 5px 0;
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 60px;
}

/* أنماط عناصر قائمة الأيقونات في الأسفل */
.nav-items {
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 100%;
  height: 100%;
}

.nav-item {
  color: var(--text-primary);
  text-decoration: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex: 1;
  padding: 5px;
}

.nav-item:hover {
  color: var(--text-secondary);
}

.nav-item.active {
  color: var(--accent);
}

.nav-item .icon {
  font-size: 1.5rem;
  margin-bottom: 3px;
}

.nav-item .label {
  font-size: 0.7rem;
  font-weight: 500;
}

/* بطارات المنتجات */
.product-card {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
  overflow: hidden;
  transition: transform 0.3s ease;
}

/* تحسينات للجداول على الأجهزة المحمولة */
@media (max-width: 768px) {
  table {
    min-width: 600px;
    width: 100%;
  }
  
  th, td {
    padding: 8px 6px;
    font-size: 0.85rem;
  }
  
  .overflow-x-auto {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
  }
  
  .overflow-x-auto::-webkit-scrollbar {
    height: 6px;
  }
  
  .overflow-x-auto::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
  }
  
  .overflow-x-auto::-webkit-scrollbar-track {
    background-color: rgba(0, 0, 0, 0.05);
  }
  
  /* إخفاء نص زر العرض على الشاشات الصغيرة جدًا */
  .hidden.sm:inline {
    display: none;
  }
  
  @media (min-width: 640px) {
    .hidden.sm:inline {
      display: inline;
    }
  }
}

.product-card:hover {
  transform: translateY(-3px);
}

.product-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.product-info {
  padding: 10px;
}

.product-title {
  font-weight: 600;
  margin-bottom: 5px;
}

.product-price {
  color: var(--accent);
  font-weight: 700;
  font-size: 1.1rem;
}

/* تحسينات للأجهزة الصغيرة جداً */
@media (max-width: 320px) {
  body {
    font-size: 12px;
  }

  .search-bar {
    padding: 10px 12px;
  }

  .search-container {
    padding: 6px 12px;
  }

  .search-input {
    font-size: 13px;
  }

  .search-icon {
    font-size: 16px;
  }

  .bottom-nav {
    height: 55px;
  }

  .nav-item .icon {
    font-size: 1.3rem;
  }

  .nav-item .label {
    font-size: 0.65rem;
  }

  .product-card {
    margin-bottom: 10px;
  }

  .product-image {
    height: 180px;
  }
}

/* تحسينات للأجهزة الصغيرة */
@media (max-width: 375px) {
  body {
    font-size: 14px;
  }

  .container {
    padding: 12px;
  }

  h1, h2, h3 {
    font-size: 1.1rem;
  }

  .btn, button {
    padding: 8px 14px;
    font-size: 0.875rem;
  }
}

/* تحسينات للأجهزة متوسطة الحجم */
@media (max-width: 414px) {
  .container {
    padding: 14px;
  }

  h1, h2, h3 {
    font-size: 1.2rem;
  }

  .btn, button {
    padding: 10px 16px;
    font-size: 0.9rem;
  }
}

/* تحسينات للأجهزة الكبيرة */
@media (min-width: 768px) {
  .container {
    padding: 20px;
  }

  h1, h2, h3 {
    font-size: 1.6rem;
  }

  .btn, button {
    padding: 16px 22px;
    font-size: 1.2rem;
  }
}

/* تحسينات للأجهزة اللوحية */
@media (min-width: 1024px) {
  .container {
    padding: 24px;
  }

  h1, h2, h3 {
    font-size: 1.8rem;
  }

  .btn, button {
    padding: 18px 24px;
    font-size: 1.3rem;
  }
}

/* تحسينات للأجهزة اللوحية الكبيرة */
@media (min-width: 1280px) {
  .container {
    padding: 32px;
  }

  h1, h2, h3 {
    font-size: 2rem;
  }

  .btn, button {
    padding: 20px 28px;
    font-size: 1.4rem;
  }

  .search-bar {
    padding: 15px 20px;
  }

  .search-container {
    padding: 10px 18px;
  }

  .search-input {
    font-size: 15px;
  }

  .search-icon {
    font-size: 20px;
  }

  .bottom-nav {
    height: 65px;
  }

  .nav-item .icon {
    font-size: 1.6rem;
  }

  .nav-item .label {
    font-size: 0.75rem;
  }
}

/* تحسينات للأجهزة التي تعمل باللمس */
@media (hover: none) {
  button:hover {
    background-color: inherit;
  }

  a:hover {
    color: inherit;
  }

  .search-container {
    padding: 10px 15px;
  }

  .nav-item {
    padding: 12px 8px;
  }

  .nav-item .icon {
    margin-bottom: 5px;
  }
}

/* تحسينات للأجهزة التي تدعم الفأرة */
@media (hover: hover) {
  .search-container:hover {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 20px;
  }

  .nav-item:hover {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
  }
}
