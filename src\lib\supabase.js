import { createClient } from '@supabase/supabase-js';

// إعدادات الاتصال بـ Supabase
// يجب استبدال هذه القيم بالقيم الحقيقية من حساب Supabase الخاص بك
const supabaseUrl = 'YOUR_SUPABASE_URL';
const supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY';

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// دوال مساعدة للتعامل مع الأخطاء
export const handleSupabaseError = (error) => {
  console.error('Supabase error:', error);
  return error.message || 'حدث خطأ أثناء الاتصال بقاعدة البيانات';
};

// دالة للتحقق من اتصال Supabase
export const checkSupabaseConnection = async () => {
  try {
    const { data, error } = await supabase.from('products').select('count');
    if (error) throw error;
    return true;
  } catch (error) {
    console.error('فشل الاتصال بـ Supabase:', error);
    return false;
  }
};
