import React, { useState, useEffect } from "react";
import base44 from "../api/base44Client";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import { createPageUrl } from "../utils";
import { Card, CardContent, CardHeader, CardTitle } from "../components/ui/Card";
import { Button } from "../components/ui/Button";
import { Badge } from "../components/ui/Badge";
import { Input } from "../components/ui/Input";
import {
  User,
  Mail,
  Phone,
  MapPin,
  CreditCard,
  LogOut,
  TrendingUp,
  Package,
  ShoppingCart,
  Heart,
  Edit,
  Save,
  X
} from "lucide-react";
import { Skeleton } from "../components/ui/Skeleton";

export default function Account() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [user, setUser] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    full_name: '',
    email: '',
    phone: '',
    address: ''
  });

  useEffect(() => {
    loadUser();
  }, []);

  const loadUser = async () => {
    try {
      const currentUser = await base44.auth.me();
      setUser(currentUser);
      setFormData({
        full_name: currentUser.full_name || '',
        email: currentUser.email || '',
        phone: currentUser.phone || '',
        address: currentUser.address || ''
      });
    } catch (error) {
      console.error("Error loading user:", error);
    }
  };

  const { data: orders = [], isLoading: ordersLoading } = useQuery({
    queryKey: ['user-orders'],
    queryFn: () => base44.entities.Order.filter({ marketer_email: user?.email }, '-created_date'),
    enabled: !!user?.email,
  });

  const { data: commissions = [] } = useQuery({
    queryKey: ['user-commissions'],
    queryFn: () => base44.entities.Commission.filter({ marketer_email: user?.email }, '-created_date'),
    enabled: !!user?.email,
  });

  const updateProfileMutation = useMutation({
    mutationFn: async (data) => {
      // في تطبيق حقيقي، ستقوم بتحديث بيانات المستخدم عبر API
      // await base44.auth.updateProfile(data);

      // مؤقتًا: قيمة وهمية
      return new Promise(resolve => {
        setTimeout(() => {
          resolve({
            id: user.id,
            ...data
          });
        }, 500);
      });
    },
    onSuccess: (updatedUser) => {
      setUser(updatedUser);
      setIsEditing(false);
      queryClient.invalidateQueries(['user']);
    },
  });

  const signOutMutation = useMutation({
    mutationFn: async () => {
      await base44.auth.signOut();
    },
    onSuccess: () => {
      navigate('/');
    },
  });

  const handleEditToggle = () => {
    if (isEditing) {
      // إعادة تعيين البيانات عند الإلغاء
      setFormData({
        full_name: user.full_name || '',
        email: user.email || '',
        phone: user.phone || '',
        address: user.address || ''
      });
    }
    setIsEditing(!isEditing);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSaveProfile = () => {
    updateProfileMutation.mutate(formData);
  };

  const totalOrders = orders.length;
  const totalCommission = commissions.reduce((sum, commission) => sum + commission.amount, 0);
  const completedOrders = orders.filter(order => order.status === 'تم التسليم').length;

  return (
    <div className="min-h-screen p-4">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-14 h-14 bg-black rounded-2xl flex items-center justify-center shadow-2xl border-2 border-yellow-400">
            <User className="w-8 h-8 text-yellow-400" />
          </div>
          <div>
            <h1 className="text-3xl font-black text-gray-900" style={{ fontFamily: 'Cairo, sans-serif' }}>حسابي</h1>
            <p className="text-gray-700 font-semibold">معلوماتك الشخصية</p>
          </div>
        </div>
      </div>

      {/* Profile Card */}
      <Card className="mb-6 border-2 border-yellow-300 shadow-xl">
        <CardHeader className="pb-3">
          <div className="flex justify-between items-center">
            <CardTitle className="text-xl font-black text-gray-900" style={{ fontFamily: 'Cairo, sans-serif' }}>
              الملف الشخصي
            </CardTitle>
            <div className="flex gap-2">
              {isEditing ? (
                <>
                  <Button
                    onClick={handleSaveProfile}
                    size="sm"
                    className="bg-green-600 hover:bg-green-700 text-white font-bold"
                    disabled={updateProfileMutation.isLoading}
                  >
                    <Save className="w-4 h-4 ml-1" />
                    حفظ
                  </Button>
                  <Button
                    onClick={handleEditToggle}
                    variant="outline"
                    size="sm"
                    className="text-red-600 hover:text-red-700 hover:bg-red-50 border-red-300 font-bold"
                  >
                    <X className="w-4 h-4 ml-1" />
                    إلغاء
                  </Button>
                </>
              ) : (
                <Button
                  onClick={handleEditToggle}
                  variant="outline"
                  size="sm"
                  className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 border-blue-300 font-bold"
                >
                  <Edit className="w-4 h-4 ml-1" />
                  تعديل
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {user ? (
            <div className="space-y-4">
              {/* Avatar */}
              <div className="flex justify-center mb-4">
                <div className="w-24 h-24 bg-black rounded-full flex items-center justify-center border-4 border-yellow-400">
                  <span className="text-yellow-400 font-black text-3xl">
                    {user.full_name?.charAt(0) || user.email?.charAt(0) || 'U'}
                  </span>
                </div>
              </div>

              {/* User Info */}
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <User className="w-5 h-5 text-gray-500" />
                  {isEditing ? (
                    <Input
                      name="full_name"
                      value={formData.full_name}
                      onChange={handleInputChange}
                      className="flex-1"
                    />
                  ) : (
                    <div className="flex-1">
                      <p className="text-sm text-gray-500">الاسم</p>
                      <p className="font-bold text-gray-900">{user.full_name || 'غير محدد'}</p>
                    </div>
                  )}
                </div>

                <div className="flex items-center gap-3">
                  <Mail className="w-5 h-5 text-gray-500" />
                  <div className="flex-1">
                    <p className="text-sm text-gray-500">البريد الإلكتروني</p>
                    <p className="font-bold text-gray-900">{user.email || 'غير محدد'}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Phone className="w-5 h-5 text-gray-500" />
                  {isEditing ? (
                    <Input
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="flex-1"
                    />
                  ) : (
                    <div className="flex-1">
                      <p className="text-sm text-gray-500">رقم الهاتف</p>
                      <p className="font-bold text-gray-900">{user.phone || 'غير محدد'}</p>
                    </div>
                  )}
                </div>

                <div className="flex items-center gap-3">
                  <MapPin className="w-5 h-5 text-gray-500" />
                  {isEditing ? (
                    <Input
                      name="address"
                      value={formData.address}
                      onChange={handleInputChange}
                      className="flex-1"
                    />
                  ) : (
                    <div className="flex-1">
                      <p className="text-sm text-gray-500">العنوان</p>
                      <p className="font-bold text-gray-900">{user.address || 'غير محدد'}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ) : (
            <Skeleton className="h-64 w-full" />
          )}
        </CardContent>
      </Card>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card className="border-2 border-blue-200 shadow-xl">
          <CardContent className="p-4 text-center">
            <ShoppingCart className="w-8 h-8 text-blue-600 mx-auto mb-2" />
            <h3 className="font-black text-2xl text-gray-900">{totalOrders}</h3>
            <p className="text-sm text-gray-600 font-semibold">إجمالي الطلبات</p>
          </CardContent>
        </Card>

        <Card className="border-2 border-green-200 shadow-xl">
          <CardContent className="p-4 text-center">
            <TrendingUp className="w-8 h-8 text-green-600 mx-auto mb-2" />
            <h3 className="font-black text-2xl text-gray-900">{totalCommission.toFixed(2)} ج</h3>
            <p className="text-sm text-gray-600 font-semibold">إجمالي العمولات</p>
          </CardContent>
        </Card>

        <Card className="border-2 border-purple-200 shadow-xl">
          <CardContent className="p-4 text-center">
            <Package className="w-8 h-8 text-purple-600 mx-auto mb-2" />
            <h3 className="font-black text-2xl text-gray-900">{completedOrders}</h3>
            <p className="text-sm text-gray-600 font-semibold">الطلبات المكتملة</p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Orders */}
      <Card className="mb-6 border-2 border-yellow-300 shadow-xl">
        <CardHeader className="pb-3">
          <CardTitle className="text-xl font-black text-gray-900" style={{ fontFamily: 'Cairo, sans-serif' }}>
            الطلبات الأخيرة
          </CardTitle>
        </CardHeader>
        <CardContent>
          {ordersLoading ? (
            <div className="space-y-3">
              {[1, 2, 3].map((i) => (
                <Skeleton key={i} className="h-16 w-full" />
              ))}
            </div>
          ) : orders.length === 0 ? (
            <div className="text-center py-8">
              <ShoppingCart className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-xl font-black text-gray-700 mb-2" style={{ fontFamily: 'Cairo, sans-serif' }}>لا توجد طلبات</h3>
              <p className="text-gray-500 font-semibold">لم تقم بإنشاء أي طلبات بعد</p>
            </div>
          ) : (
            <div className="space-y-3">
              {orders.slice(0, 5).map((order) => (
                <div key={order.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-bold text-gray-900">طلب #{order.id}</p>
                    <p className="text-sm text-gray-600">{new Date(order.created_date).toLocaleDateString('ar-SA')}</p>
                  </div>
                  <Badge className={
                    order.status === 'تم التسليم' ? 'bg-green-100 text-green-800' :
                    order.status === 'قيد المعالجة' ? 'bg-blue-100 text-blue-800' :
                    'bg-gray-100 text-gray-800'
                  }>
                    {order.status}
                  </Badge>
                </div>
              ))}
              {orders.length > 5 && (
                <Button
                  onClick={() => navigate(createPageUrl("Orders"))}
                  variant="outline"
                  className="w-full mt-2 font-bold"
                >
                  عرض جميع الطلبات
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Sign Out Button */}
      <Button
        onClick={() => signOutMutation.mutate()}
        className="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-3"
        disabled={signOutMutation.isLoading}
      >
        <LogOut className="w-5 h-5 ml-2" />
        تسجيل الخروج
      </Button>
    </div>
  );
}
