import React, { useState, useEffect } from "react";
import base44 from "../api/base44Client";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import { createPageUrl } from "../utils";
import { Card, CardContent } from "../components/ui/Card";
import { Button } from "../components/ui/Button";
import { Badge } from "../components/ui/Badge";
import {
  ShoppingBag,
  Trash2,
  Plus,
  Minus,
  ShoppingCart,
  TrendingUp,
  Package
} from "lucide-react";
import { Skeleton } from "../components/ui/Skeleton";

export default function Cart() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [user, setUser] = useState(null);

  useEffect(() => {
    loadUser();
  }, []);

  const loadUser = async () => {
    try {
      const currentUser = await base44.auth.me();
      setUser(currentUser);
    } catch (error) {
      console.error("Error loading user:", error);
    }
  };

  const { data: cartItems = [], isLoading } = useQuery({
    queryKey: ['cart'],
    queryFn: () => base44.entities.CartItem.filter({ marketer_email: user?.email }, '-created_date'),
    enabled: !!user?.email,
  });

  const removeFromCartMutation = useMutation({
    mutationFn: async (itemId) => {
      await base44.entities.CartItem.delete(itemId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['cart']);
    },
  });

  const updateQuantityMutation = useMutation({
    mutationFn: async ({ itemId, quantity }) => {
      await base44.entities.CartItem.update(itemId, { quantity });
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['cart']);
    },
  });

  const totalPrice = cartItems.reduce((sum, item) => sum + (item.product_price * item.quantity), 0);
  const totalCommission = cartItems.reduce((sum, item) => sum + (item.commission_amount * item.quantity), 0);

  const handleQuantityChange = (item, delta) => {
    const newQuantity = item.quantity + delta;
    if (newQuantity > 0) {
      updateQuantityMutation.mutate({ itemId: item.id, quantity: newQuantity });
    }
  };

  const handleCheckout = () => {
    if (cartItems.length > 0) {
      navigate(createPageUrl("NewOrder"));
    }
  };

  return (
    <div className="min-h-screen p-4">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-14 h-14 bg-black rounded-2xl flex items-center justify-center shadow-2xl border-2 border-yellow-400">
            <ShoppingBag className="w-8 h-8 text-yellow-400" />
          </div>
          <div>
            <h1 className="text-3xl font-black text-gray-900" style={{ fontFamily: 'Cairo, sans-serif' }}>سلة الطلبات</h1>
            <p className="text-gray-700 font-semibold">منتجاتك المختارة</p>
          </div>
        </div>
      </div>

      {isLoading ? (
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} className="h-32 w-full rounded-2xl" />
          ))}
        </div>
      ) : cartItems.length === 0 ? (
        <Card className="border-2 border-dashed border-gray-300">
          <CardContent className="p-16 text-center">
            <ShoppingCart className="w-24 h-24 text-gray-300 mx-auto mb-4" />
            <h3 className="text-2xl font-black text-gray-700 mb-2" style={{ fontFamily: 'Cairo, sans-serif' }}>السلة فارغة</h3>
            <p className="text-gray-500 font-semibold mb-6">لم تضف أي منتجات بعد</p>
            <Button
              onClick={() => navigate(createPageUrl("Store"))}
              className="bg-black hover:bg-gray-900 text-yellow-400 font-black border-2 border-yellow-400"
            >
              <Package className="w-5 h-5 ml-2" />
              تصفح المنتجات
            </Button>
          </CardContent>
        </Card>
      ) : (
        <>
          {/* Cart Items */}
          <div className="space-y-4 mb-6">
            {cartItems.map((item) => (
              <Card
                key={item.id}
                className="border-2 border-yellow-300 shadow-xl hover:shadow-2xl transition-all"
              >
                <CardContent className="p-4">
                  <div className="flex gap-4">
                    {/* Product Image */}
                    <div className="w-24 h-24 bg-gradient-to-br from-yellow-50 to-amber-50 rounded-xl overflow-hidden flex-shrink-0 border-2 border-yellow-200">
                      {item.product_image ? (
                        <img
                          src={item.product_image}
                          alt={item.product_name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <Package className="w-10 h-10 text-yellow-300" />
                        </div>
                      )}
                    </div>

                    {/* Product Info */}
                    <div className="flex-1">
                      <h3 className="font-black text-lg text-gray-900 mb-2 line-clamp-2" style={{ fontFamily: 'Cairo, sans-serif' }}>
                        {item.product_name}
                      </h3>
                      <div className="flex items-center gap-4 mb-2">
                        <span className="text-xl font-black text-blue-600">{item.product_price} ج</span>
                        <Badge className="bg-green-100 text-green-800 font-bold">
                          <TrendingUp className="w-3 h-3 ml-1" />
                          عمولة: {item.commission_amount} ج
                        </Badge>
                      </div>

                      {/* Quantity Controls */}
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-2 bg-gray-100 rounded-lg p-1">
                          <button
                            onClick={() => handleQuantityChange(item, -1)}
                            className="w-8 h-8 bg-white rounded-lg flex items-center justify-center hover:bg-gray-200 transition-colors border border-gray-300"
                            disabled={item.quantity <= 1}
                          >
                            <Minus className="w-4 h-4 text-gray-700" />
                          </button>
                          <span className="font-black text-lg text-gray-900 w-8 text-center">{item.quantity}</span>
                          <button
                            onClick={() => handleQuantityChange(item, 1)}
                            className="w-8 h-8 bg-white rounded-lg flex items-center justify-center hover:bg-gray-200 transition-colors border border-gray-300"
                          >
                            <Plus className="w-4 h-4 text-gray-700" />
                          </button>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => removeFromCartMutation.mutate(item.id)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50 border-red-300 font-bold"
                        >
                          <Trash2 className="w-4 h-4 ml-1" />
                          حذف
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Summary */}
          <Card className="border-2 border-yellow-400 shadow-2xl sticky bottom-24 bg-white">
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="font-bold text-gray-700">عدد المنتجات:</span>
                  <span className="font-black text-xl text-gray-900">{cartItems.length}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="font-bold text-gray-700">إجمالي السعر:</span>
                  <span className="font-black text-2xl text-blue-600">{totalPrice.toFixed(2)} ج</span>
                </div>
                <div className="flex justify-between items-center bg-green-50 p-3 rounded-xl border-2 border-green-200">
                  <span className="font-bold text-gray-700">إجمالي عمولتك:</span>
                  <span className="font-black text-2xl text-green-600 flex items-center gap-1">
                    <TrendingUp className="w-6 h-6" />
                    {totalCommission.toFixed(2)} ج
                  </span>
                </div>
                <Button
                  onClick={handleCheckout}
                  className="w-full bg-black hover:bg-gray-900 text-yellow-400 font-black text-lg py-6 border-2 border-yellow-400 shadow-xl"
                >
                  <ShoppingCart className="w-5 h-5 ml-2" />
                  إتمام الطلب
                </Button>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
