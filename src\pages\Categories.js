import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { createPageUrl } from "../utils";
import { Card, CardContent } from "../components/ui/Card";
import { Badge } from "../components/ui/Badge";
import {
  Shirt,
  ShoppingBag,
  Package,
  Watch,
  Footprints,
  ChevronRight,
  Store,
  ArrowRight
} from "lucide-react";

const categoryIcons = {
  "رجالي": { icon: Shirt, color: "from-blue-500 to-blue-700", count: 42 },
  "حريمي": { icon: ShoppingBag, color: "from-pink-500 to-pink-700", count: 38 },
  "أحذية": { icon: Footprints, color: "from-green-500 to-green-700", count: 25 },
  "شنط": { icon: Package, color: "from-purple-500 to-purple-700", count: 18 },
  "إكسسوارات": { icon: Watch, color: "from-yellow-500 to-yellow-700", count: 32 }
};

export default function Categories() {
  const navigate = useNavigate();
  const [selectedCategory, setSelectedCategory] = useState(null);

  const handleCategoryClick = (category) => {
    setSelectedCategory(category);
    navigate(`${createPageUrl("Store")}?category=${encodeURIComponent(category)}`);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-yellow-50 via-yellow-100 to-amber-50 pb-20">
      <style>{`
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700;800;900&display=swap');
        * {
          direction: rtl;
          font-family: 'Cairo', 'Segoe UI', sans-serif;
        }
      `}</style>

      {/* Header */}
      <div className="bg-gradient-to-r from-black to-gray-800 text-yellow-400 p-6 shadow-xl">
        <div className="flex items-center gap-3">
          <Store className="w-8 h-8" />
          <h1 className="text-2xl md:text-3xl font-black">الفئات</h1>
        </div>
        <p className="mt-2 text-yellow-300 font-semibold">اختر الفئة التي تريدها</p>
      </div>

      {/* Categories Grid */}
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Object.entries(categoryIcons).map(([category, { icon: Icon, color, count }]) => (
            <Card
              key={category}
              className={`overflow-hidden border-2 border-gray-200 shadow-xl hover:shadow-2xl transition-all hover:scale-105 cursor-pointer rounded-2xl bg-white`}
              onClick={() => handleCategoryClick(category)}
            >
              <div className={`h-40 bg-gradient-to-br ${color} relative overflow-hidden`}>
                <div className="absolute top-4 left-4">
                  <div className="bg-white bg-opacity-90 rounded-full p-3 shadow-lg">
                    <Icon className="w-8 h-8 text-gray-800" />
                  </div>
                </div>
                <div className="absolute bottom-4 right-4">
                  <Badge className="bg-white text-gray-800 font-bold text-lg px-3 py-1">
                    {count} منتج
                  </Badge>
                </div>
              </div>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <h3 className="font-black text-xl text-gray-900">{category}</h3>
                  <div className="bg-gray-100 rounded-full p-2">
                    <ArrowRight className="w-5 h-5 text-gray-700" />
                  </div>
                </div>
                <p className="mt-2 text-gray-600 font-semibold">
                  تصفح أفضل منتجات {category}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
