import React, { useState, useEffect } from "react";
import base44 from "../api/base44Client";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import { createPageUrl } from "../utils";
import { Card, CardContent } from "../components/ui/Card";
import { Button } from "../components/ui/Button";
import { Badge } from "../components/ui/Badge";
import {
  Heart,
  ShoppingCart,
  TrendingUp,
  Package,
  ShoppingBag
} from "lucide-react";
import { Skeleton } from "../components/ui/Skeleton";

export default function Favorites() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [user, setUser] = useState(null);

  useEffect(() => {
    loadUser();
  }, []);

  const loadUser = async () => {
    try {
      const currentUser = await base44.auth.me();
      setUser(currentUser);
    } catch (error) {
      console.error("Error loading user:", error);
    }
  };

  const { data: favorites = [], isLoading, refetch } = useQuery({
    queryKey: ['favorites'],
    queryFn: () => base44.entities.Favorite.filter({ marketer_email: user?.email }, '-created_date'),
    enabled: !!user?.email,
  });



  const addToCartMutation = useMutation({
    mutationFn: async (favorite) => {
      return await base44.entities.CartItem.create({
        product_id: favorite.product_id,
        product_name: favorite.product_name,
        product_price: favorite.product_price,
        product_image: favorite.product_image,
        commission_amount: favorite.commission_amount,
        quantity: 1,
        marketer_id: user?.id,
        marketer_email: user?.email
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['cart']);

      // عرض رسالة تأكيد
      const notification = document.createElement('div');
      notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 flex items-center space-x-2 rtl:space-x-reverse animate-pulse';
      notification.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
        </svg>
        <span>تم إضافة المنتج للسلة!</span>
      `;
      document.body.appendChild(notification);

      // إزالة الإشعار بعد 3 ثوانٍ
      setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transition = 'opacity 0.5s';
        setTimeout(() => {
          if (document.body.contains(notification)) {
            document.body.removeChild(notification);
          }
        }, 500);
      }, 3000);
    },
  });

  const handleRemoveFromFavorites = (favoriteId) => {
    base44.entities.Favorite.delete(favoriteId)
      .then(() => queryClient.invalidateQueries(['favorites']));
  };

  const handleAddToCart = (favorite) => {
    addToCartMutation.mutate(favorite);
  };

  return (
    <div className="min-h-screen p-4">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-14 h-14 bg-black rounded-2xl flex items-center justify-center shadow-2xl border-2 border-yellow-400">
            <Heart className="w-8 h-8 text-yellow-400" fill="currentColor" />
          </div>
          <div>
            <h1 className="text-3xl font-black text-gray-900" style={{ fontFamily: 'Cairo, sans-serif' }}>المفضلة</h1>
            <p className="text-gray-700 font-semibold">منتجاتك المفضلة</p>
          </div>
        </div>
      </div>

      {isLoading ? (
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} className="h-32 w-full rounded-2xl" />
          ))}
        </div>
      ) : favorites.length === 0 ? (
        <Card className="border-2 border-dashed border-gray-300">
          <CardContent className="p-16 text-center">
            <Heart className="w-24 h-24 text-gray-300 mx-auto mb-4" />
            <h3 className="text-2xl font-black text-gray-700 mb-2" style={{ fontFamily: 'Cairo, sans-serif' }}>المفضلة فارغة</h3>
            <p className="text-gray-500 font-semibold mb-6">لم تضف أي منتجات للمفضلة بعد</p>
            <Button
              onClick={() => navigate(createPageUrl("Store"))}
              className="bg-black hover:bg-gray-900 text-yellow-400 font-black border-2 border-yellow-400"
            >
              <Package className="w-5 h-5 ml-2" />
              تصفح المنتجات
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {favorites.map((favorite) => (
            <Card
              key={favorite.id}
              className="border-2 border-yellow-300 shadow-xl hover:shadow-2xl transition-all"
            >
              <CardContent className="p-4">
                <div className="flex gap-4">
                  {/* Product Image */}
                  <div className="w-24 h-24 bg-gradient-to-br from-yellow-50 to-amber-50 rounded-xl overflow-hidden flex-shrink-0 border-2 border-yellow-200">
                    {favorite.product_image ? (
                      <img
                        src={favorite.product_image}
                        alt={favorite.product_name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <Package className="w-10 h-10 text-yellow-300" />
                      </div>
                    )}
                  </div>

                  {/* Product Info */}
                  <div className="flex-1">
                    <h3 className="font-black text-lg text-gray-900 mb-2 line-clamp-2" style={{ fontFamily: 'Cairo, sans-serif' }}>
                      {favorite.product_name}
                    </h3>
                    <div className="flex items-center gap-4 mb-3">
                      <span className="text-xl font-black text-blue-600">{favorite.product_price} ج</span>
                      <Badge className="bg-green-100 text-green-800 font-bold">
                        <TrendingUp className="w-3 h-3 ml-1" />
                        عمولة: {favorite.commission_amount} ج
                      </Badge>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex items-center gap-3">
                      <Button
                        onClick={() => handleAddToCart(favorite)}
                        className="bg-black hover:bg-gray-900 text-yellow-400 font-bold border-2 border-yellow-400"
                      >
                        <ShoppingCart className="w-4 h-4 ml-2" />
                        أضف للسلة
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => handleRemoveFromFavorites(favorite.id)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50 border-red-300 font-bold"
                      >
                        <Heart className="w-4 h-4 ml-1" fill="currentColor" />
                        إزالة
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
