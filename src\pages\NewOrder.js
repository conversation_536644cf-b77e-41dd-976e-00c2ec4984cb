import React, { useState, useEffect } from "react";
import base44 from "../api/base44Client";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import { createPageUrl } from "../utils";
import { Card, CardContent, CardHeader, CardTitle } from "../components/ui/Card";
import { Button } from "../components/ui/Button";
import { Badge } from "../components/ui/Badge";
import { Input } from "../components/ui/Input";
import {
  ShoppingCart,
  Package,
  TrendingUp,
  User,
  MapPin,
  Phone,
  Mail,
  CreditCard,
  ArrowLeft,
  CheckCircle
} from "lucide-react";
import { Skeleton } from "../components/ui/Skeleton";

export default function NewOrder() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [user, setUser] = useState(null);
  const [orderStep, setOrderStep] = useState(1); // 1: تأكيد الطلب, 2: بيانات العميل, 3: الدفع, 4: تأكيد
  const [customerData, setCustomerData] = useState({
    name: '',
    phone: '',
    email: '',
    address: '',
    notes: ''
  });
  const [paymentMethod, setPaymentMethod] = useState('cash');

  useEffect(() => {
    loadUser();
  }, []);

  const loadUser = async () => {
    try {
      const currentUser = await base44.auth.me();
      setUser(currentUser);
    } catch (error) {
      console.error("Error loading user:", error);
    }
  };

  const { data: cartItems = [], isLoading } = useQuery({
    queryKey: ['cart'],
    queryFn: () => base44.entities.CartItem.filter({ marketer_email: user?.email }, '-created_date'),
    enabled: !!user?.email,
  });

  const createOrderMutation = useMutation({
    mutationFn: async (orderData) => {
      // إنشاء الطلب عبر API
      const response = await base44.entities.Order.create(orderData);
      return response;
    },
    onSuccess: (newOrder) => {
      // تفريغ السلة بعد إنشاء الطلب
      Promise.all(
        cartItems.map(item => base44.entities.CartItem.delete(item.id))
      ).then(() => {
        queryClient.invalidateQueries(['cart']);
        queryClient.invalidateQueries(['user-orders']); // تحديث قائمة الطلبات
        setOrderStep(4); // الانتقال إلى خطوة التأكيد
      });
    },
  });

  const totalPrice = cartItems.reduce((sum, item) => sum + (item.product_price * item.quantity), 0);
  const totalCommission = cartItems.reduce((sum, item) => sum + (item.commission_amount * item.quantity), 0);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setCustomerData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleNextStep = () => {
    if (orderStep < 4) {
      setOrderStep(orderStep + 1);
    }
  };

  const handlePrevStep = () => {
    if (orderStep > 1) {
      setOrderStep(orderStep - 1);
    }
  };

  const handleCreateOrder = () => {
    const orderData = {
      marketer_id: user?.id,
      marketer_email: user?.email,
      customer_name: customerData.name,
      customer_phone: customerData.phone,
      customer_email: customerData.email,
      customer_address: customerData.address,
      notes: customerData.notes,
      items: cartItems.map(item => ({
        product_id: item.product_id,
        product_name: item.product_name,
        product_price: item.product_price,
        quantity: item.quantity,
        commission_amount: item.commission_amount
      })),
      total_price: totalPrice,
      commission_amount: totalCommission,
      payment_method: paymentMethod
    };

    createOrderMutation.mutate(orderData);
  };

  const isStepValid = () => {
    switch (orderStep) {
      case 1:
        return cartItems.length > 0;
      case 2:
        return customerData.name && customerData.phone && customerData.address;
      case 3:
        return true; // لا يوجد تحقق خاص لطريقة الدفع
      default:
        return false;
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen p-4">
        <div className="space-y-4">
          <Skeleton className="h-12 w-48" />
          <Skeleton className="h-64 w-full" />
          <Skeleton className="h-12 w-full" />
        </div>
      </div>
    );
  }

  if (cartItems.length === 0) {
    return (
      <div className="min-h-screen p-4 flex items-center justify-center">
        <Card className="max-w-md w-full border-2 border-dashed border-gray-300">
          <CardContent className="p-16 text-center">
            <ShoppingCart className="w-24 h-24 text-gray-300 mx-auto mb-4" />
            <h3 className="text-2xl font-black text-gray-700 mb-2" style={{ fontFamily: 'Cairo, sans-serif' }}>السلة فارغة</h3>
            <p className="text-gray-500 font-semibold mb-6">لا يمكن إنشاء طلب بدون منتجات</p>
            <Button
              onClick={() => navigate(createPageUrl("Store"))}
              className="bg-black hover:bg-gray-900 text-yellow-400 font-black border-2 border-yellow-400"
            >
              <Package className="w-5 h-5 ml-2" />
              تصفح المنتجات
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-4">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate(createPageUrl("Cart"))}
            className="text-gray-700 hover:bg-gray-100 border-gray-300"
          >
            <ArrowLeft className="w-4 h-4 ml-1" />
            العودة للسلة
          </Button>
          <div className="w-14 h-14 bg-black rounded-2xl flex items-center justify-center shadow-2xl border-2 border-yellow-400">
            <ShoppingCart className="w-8 h-8 text-yellow-400" />
          </div>
          <div>
            <h1 className="text-3xl font-black text-gray-900" style={{ fontFamily: 'Cairo, sans-serif' }}>إنشاء طلب جديد</h1>
            <p className="text-gray-700 font-semibold">أكمل بيانات الطلب</p>
          </div>
        </div>
      </div>

      {/* Progress Steps */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {[1, 2, 3, 4].map((step) => (
            <div key={step} className="flex items-center">
              <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                step <= orderStep ? 'bg-black text-yellow-400' : 'bg-gray-200 text-gray-500'
              }`}>
                {step < orderStep ? (
                  <CheckCircle className="w-5 h-5" />
                ) : (
                  step
                )}
              </div>
              {step < 4 && (
                <div className={`flex-1 h-1 mx-2 ${
                  step < orderStep ? 'bg-black' : 'bg-gray-200'
                }`} />
              )}
            </div>
          ))}
        </div>
        <div className="flex justify-between mt-2">
          <span className={`text-sm font-bold ${
            orderStep >= 1 ? 'text-black' : 'text-gray-500'
          }`}>تأكيد المنتجات</span>
          <span className={`text-sm font-bold ${
            orderStep >= 2 ? 'text-black' : 'text-gray-500'
          }`}>بيانات العميل</span>
          <span className={`text-sm font-bold ${
            orderStep >= 3 ? 'text-black' : 'text-gray-500'
          }`}>طريقة الدفع</span>
          <span className={`text-sm font-bold ${
            orderStep >= 4 ? 'text-black' : 'text-gray-500'
          }`}>تأكيد الطلب</span>
        </div>
      </div>

      {/* Step Content */}
      <Card className="mb-6 border-2 border-yellow-300 shadow-xl">
        <CardContent className="p-6">
          {orderStep === 1 && (
            <div>
              <h2 className="text-xl font-black text-gray-900 mb-4" style={{ fontFamily: 'Cairo, sans-serif' }}>تأكيد المنتجات</h2>
              <div className="space-y-3 mb-6">
                {cartItems.map((item) => (
                  <div key={item.id} className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
                    <div className="w-16 h-16 bg-gradient-to-br from-yellow-50 to-amber-50 rounded-lg overflow-hidden flex-shrink-0 border-2 border-yellow-200">
                      {item.product_image ? (
                        <img
                          src={item.product_image}
                          alt={item.product_name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <Package className="w-8 h-8 text-yellow-300" />
                        </div>
                      )}
                    </div>
                    <div className="flex-1">
                      <h3 className="font-black text-gray-900 line-clamp-1">{item.product_name}</h3>
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-sm font-bold text-blue-600">{item.product_price} ج</span>
                        <Badge className="bg-green-100 text-green-800 font-bold text-xs">
                          <TrendingUp className="w-3 h-3 ml-1" />
                          عمولة: {item.commission_amount} ج
                        </Badge>
                        <span className="text-sm font-bold text-gray-700">الكمية: {item.quantity}</span>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-black text-gray-900">{(item.product_price * item.quantity).toFixed(2)} ج</p>
                      <p className="text-sm font-bold text-green-600">{(item.commission_amount * item.quantity).toFixed(2)} ج عمولة</p>
                    </div>
                  </div>
                ))}
              </div>

              <div className="bg-yellow-50 p-4 rounded-xl border-2 border-yellow-200">
                <div className="flex justify-between items-center mb-2">
                  <span className="font-bold text-gray-700">إجمالي المنتجات:</span>
                  <span className="font-black text-xl text-gray-900">{cartItems.length}</span>
                </div>
                <div className="flex justify-between items-center mb-2">
                  <span className="font-bold text-gray-700">إجمالي السعر:</span>
                  <span className="font-black text-2xl text-blue-600">{totalPrice.toFixed(2)} ج</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="font-bold text-gray-700">إجمالي عمولتك:</span>
                  <span className="font-black text-2xl text-green-600 flex items-center gap-1">
                    <TrendingUp className="w-6 h-6" />
                    {totalCommission.toFixed(2)} ج
                  </span>
                </div>
              </div>
            </div>
          )}

          {orderStep === 2 && (
            <div>
              <h2 className="text-xl font-black text-gray-900 mb-4" style={{ fontFamily: 'Cairo, sans-serif' }}>بيانات العميل</h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">
                    <User className="w-4 h-4 inline ml-1" />
                    اسم العميل
                  </label>
                  <Input
                    name="name"
                    value={customerData.name}
                    onChange={handleInputChange}
                    placeholder="أدخل اسم العميل"
                    className="w-full"
                  />
                </div>

                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">
                    <Phone className="w-4 h-4 inline ml-1" />
                    رقم الهاتف
                  </label>
                  <Input
                    name="phone"
                    value={customerData.phone}
                    onChange={handleInputChange}
                    placeholder="أدخل رقم الهاتف"
                    className="w-full"
                  />
                </div>

                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">
                    <Mail className="w-4 h-4 inline ml-1" />
                    البريد الإلكتروني (اختياري)
                  </label>
                  <Input
                    name="email"
                    value={customerData.email}
                    onChange={handleInputChange}
                    placeholder="أدخل البريد الإلكتروني"
                    className="w-full"
                  />
                </div>

                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">
                    <MapPin className="w-4 h-4 inline ml-1" />
                    عنوان التوصيل
                  </label>
                  <Input
                    name="address"
                    value={customerData.address}
                    onChange={handleInputChange}
                    placeholder="أدخل عنوان التوصيل"
                    className="w-full"
                  />
                </div>

                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">
                    ملاحظات إضافية (اختياري)
                  </label>
                  <textarea
                    name="notes"
                    value={customerData.notes}
                    onChange={handleInputChange}
                    placeholder="أدخل أي ملاحظات إضافية"
                    className="w-full p-3 border-2 border-gray-300 rounded-lg focus:outline-none focus:border-yellow-400"
                    rows="3"
                  />
                </div>
              </div>
            </div>
          )}

          {orderStep === 3 && (
            <div>
              <h2 className="text-xl font-black text-gray-900 mb-4" style={{ fontFamily: 'Cairo, sans-serif' }}>طريقة الدفع</h2>
              <div className="space-y-3">
                <div 
                  className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                    paymentMethod === 'cash' 
                      ? 'border-yellow-400 bg-yellow-50' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setPaymentMethod('cash')}
                >
                  <div className="flex items-center">
                    <div className={`w-5 h-5 rounded-full border-2 mr-3 ${
                      paymentMethod === 'cash' 
                        ? 'border-yellow-400 bg-yellow-400' 
                        : 'border-gray-300'
                    }`}>
                      {paymentMethod === 'cash' && (
                        <div className="w-full h-full flex items-center justify-center">
                          <div className="w-2 h-2 bg-white rounded-full"></div>
                        </div>
                      )}
                    </div>
                    <div>
                      <h3 className="font-black text-gray-900">الدفع عند الاستلام</h3>
                      <p className="text-sm text-gray-600">الدفع نقدًا عند استلام المنتج</p>
                    </div>
                  </div>
                </div>

                <div 
                  className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                    paymentMethod === 'card' 
                      ? 'border-yellow-400 bg-yellow-50' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setPaymentMethod('card')}
                >
                  <div className="flex items-center">
                    <div className={`w-5 h-5 rounded-full border-2 mr-3 ${
                      paymentMethod === 'card' 
                        ? 'border-yellow-400 bg-yellow-400' 
                        : 'border-gray-300'
                    }`}>
                      {paymentMethod === 'card' && (
                        <div className="w-full h-full flex items-center justify-center">
                          <div className="w-2 h-2 bg-white rounded-full"></div>
                        </div>
                      )}
                    </div>
                    <div>
                      <h3 className="font-black text-gray-900">الدفع بالبطاقة</h3>
                      <p className="text-sm text-gray-600">الدفع باستخدام البطاقة البنكية</p>
                    </div>
                  </div>
                </div>

                <div className="mt-6 p-4 bg-blue-50 rounded-xl border-2 border-blue-200">
                  <h3 className="font-black text-blue-900 mb-2">ملخص الطلب</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm font-bold text-gray-700">عدد المنتجات:</span>
                      <span className="font-black text-gray-900">{cartItems.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm font-bold text-gray-700">إجمالي السعر:</span>
                      <span className="font-black text-blue-600">{totalPrice.toFixed(2)} ج</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm font-bold text-gray-700">عمولتك:</span>
                      <span className="font-black text-green-600">{totalCommission.toFixed(2)} ج</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm font-bold text-gray-700">طريقة الدفع:</span>
                      <span className="font-black text-gray-900">
                        {paymentMethod === 'cash' ? 'الدفع عند الاستلام' : 'الدفع بالبطاقة'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {orderStep === 4 && (
            <div className="text-center py-8">
              <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-12 h-12 text-green-600" />
              </div>
              <h2 className="text-2xl font-black text-gray-900 mb-2" style={{ fontFamily: 'Cairo, sans-serif' }}>تم إنشاء الطلب بنجاح!</h2>
              <p className="text-gray-600 font-semibold mb-6">رقم الطلب: #{createOrderMutation.data?.id || 'N/A'}</p>
              <p className="text-gray-600 mb-6">سيتم توصيل الطلب إلى العميل قريبًا</p>
              <div className="flex gap-3 justify-center">
                <Button
                  onClick={() => navigate(createPageUrl("Orders"))}
                  className="bg-black hover:bg-gray-900 text-yellow-400 font-black border-2 border-yellow-400"
                >
                  <ShoppingCart className="w-5 h-5 ml-2" />
                  عرض الطلبات
                </Button>
                <Button
                  onClick={() => navigate(createPageUrl("Store"))}
                  variant="outline"
                  className="text-gray-700 hover:bg-gray-100 border-gray-300 font-bold"
                >
                  <Package className="w-5 h-5 ml-2" />
                  العودة للمتجر
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Navigation Buttons */}
      {orderStep < 4 && (
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={handlePrevStep}
            disabled={orderStep === 1}
            className="text-gray-700 hover:bg-gray-100 border-gray-300 font-bold"
          >
            السابق
          </Button>
          <Button
            onClick={orderStep === 3 ? handleCreateOrder : handleNextStep}
            disabled={!isStepValid() || createOrderMutation.isLoading}
            className="bg-black hover:bg-gray-900 text-yellow-400 font-black border-2 border-yellow-400"
          >
            {orderStep === 3 ? 'إنشاء الطلب' : 'التالي'}
            {createOrderMutation.isLoading && (
              <div className="w-4 h-4 border-2 border-yellow-400 border-t-transparent rounded-full animate-spin mr-2"></div>
            )}
          </Button>
        </div>
      )}
    </div>
  );
}
