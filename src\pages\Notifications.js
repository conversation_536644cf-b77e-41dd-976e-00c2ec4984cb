import React from "react";
import { useNavigate } from "react-router-dom";
import { createPageUrl } from "../utils";
import { Bell, Clock, Check, X, Package, ShoppingCart, AlertCircle } from "lucide-react";
import { useNotifications } from "../hooks/useNotifications";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export default function Notifications() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  
  // استخدام هوك الإشعارات
  const { 
    notifications, 
    isLoading, 
    markAsRead, 
    deleteNotification, 
    markAllAsRead 
  } = useNotifications();

  // تحديث حالة الإشعار كمقروء
  const markAsReadMutation = useMutation({
    mutationFn: async (id) => {
      // في التطبيق الحقيقي، سيتم إرسال طلب API لتحديث حالة الإشعار
      return new Promise(resolve => setTimeout(() => resolve(id), 300));
    },
    onSuccess: (id) => {
      // تحديث البيانات المحلية
      queryClient.setQueryData(["notifications"], (oldNotifications) => 
        oldNotifications.map(notification => 
          notification.id === id ? { ...notification, read: true } : notification
        )
      );
      // تحديث عدد الإشعارات في الـ Layout
      queryClient.invalidateQueries(["notificationsCount"]);
    }
  });

  // حذف إشعار
  const deleteNotificationMutation = useMutation({
    mutationFn: async (id) => {
      // في التطبيق الحقيقي، سيتم إرسال طلب API لحذف الإشعار
      return new Promise(resolve => setTimeout(() => resolve(id), 300));
    },
    onSuccess: (id) => {
      // تحديث البيانات المحلية
      queryClient.setQueryData(["notifications"], (oldNotifications) => 
        oldNotifications.filter(notification => notification.id !== id)
      );
      // تحديث عدد الإشعارات في الـ Layout
      queryClient.invalidateQueries(["notificationsCount"]);
    }
  });

  // تحديث كل الإشعارات كمقروءة
  const markAllAsReadMutation = useMutation({
    mutationFn: async () => {
      // في التطبيق الحقيقي، سيتم إرسال طلب API لتحديث كل الإشعارات
      return new Promise(resolve => setTimeout(() => resolve(), 300));
    },
    onSuccess: () => {
      // تحديث البيانات المحلية
      queryClient.setQueryData(["notifications"], (oldNotifications) => 
        oldNotifications.map(notification => ({ ...notification, read: true }))
      );
      // تحديث عدد الإشعارات في الـ Layout
      queryClient.invalidateQueries(["notificationsCount"]);
    }
  });

  // تم استيراد markAsRead من useNotifications
  // تم استيراد deleteNotification و markAllAsRead من useNotifications

  return (
    <div className="min-h-screen bg-gradient-to-br from-yellow-50 via-yellow-100 to-amber-50 pb-20">
      <div className="fixed top-0 left-0 right-0 z-50 bg-white border-yellow-200 border-b-2 shadow-lg">
        <div className="px-4 py-3 flex items-center">
          <button 
            onClick={() => navigate(-1)} 
            className="ml-2 p-2 rounded-full bg-yellow-100 hover:bg-yellow-200 transition-colors"
          >
            <X className="w-5 h-5 text-gray-700" />
          </button>
          <h1 className="text-xl font-bold text-black">الإشعارات</h1>
          {notifications.filter(n => !n.read).length > 0 && (
            <button 
              onClick={markAllAsRead}
              className="mr-auto text-sm font-medium text-blue-600 hover:text-blue-800"
            >
              تحديد الكل كمقروء
            </button>
          )}
        </div>
      </div>

      <div className="pt-16 px-4">
        {notifications.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-20">
            <Bell className="w-16 h-16 text-gray-300 mb-4" />
            <p className="text-gray-500 text-center">لا توجد إشعارات حالياً</p>
          </div>
        ) : (
          <div className="space-y-3 mt-4">
            {notifications.map((notification) => (
              <div 
                key={notification.id}
                className={`bg-white rounded-2xl shadow-md p-4 border-2 ${
                  notification.read ? 'border-gray-200' : 'border-yellow-300 bg-yellow-50'
                } transition-all`}
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h3 className="font-semibold text-lg text-black">{notification.title}</h3>
                    <p className="text-gray-600 mt-1">{notification.message}</p>
                    <div className="flex items-center mt-2 text-gray-500 text-sm">
                      <Clock className="w-4 h-4 ml-1" />
                      {notification.time}
                    </div>
                  </div>
                  <div className="flex gap-2 mr-2">
                    {!notification.read && (
                      <button
                        onClick={() => markAsRead(notification.id)}
                        className="p-2 rounded-full bg-blue-100 hover:bg-blue-200 transition-colors"
                        title="تحديد كمقروء"
                      >
                        <Check className="w-4 h-4 text-blue-600" />
                      </button>
                    )}
                    <button
                      onClick={() => deleteNotification(notification.id)}
                      className="p-2 rounded-full bg-red-100 hover:bg-red-200 transition-colors"
                      title="حذف"
                    >
                      <X className="w-4 h-4 text-red-600" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
