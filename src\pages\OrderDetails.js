import React, { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import base44 from "../api/base44Client";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "../components/ui/Card";
import { Button } from "../components/ui/Button";
import { Badge } from "../components/ui/Badge";
import WhatsAppIcon from "../components/WhatsAppIcon";
import {
  ArrowRight,
  Calendar,
  CheckCircle,
  Clock,
  MapPin,
  Mail,
  Package,
  Phone,
  ShoppingCart,
  TrendingUp,
  Truck,
  User,
  XCircle
} from "lucide-react";
import { Skeleton } from "../components/ui/Skeleton";

export default function OrderDetails() {
  const { orderId } = useParams();
  const navigate = useNavigate();

  const { data: order, isLoading, error } = useQuery({
    queryKey: ['order', orderId],
    queryFn: () => base44.entities.Order.get(orderId),
    enabled: !!orderId,
  });

  const { data: orderItems = [] } = useQuery({
    queryKey: ['order-items', orderId],
    queryFn: () => base44.entities.OrderItem.filter({ order_id: orderId }),
    enabled: !!orderId,
  });

  const getStatusColor = (status) => {
    switch (status) {
      case 'تم التسليم':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'قيد التوصيل':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'قيد التجهيز':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'تم الإلغاء':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'تم التسليم':
        return <CheckCircle className="w-4 h-4" />;
      case 'قيد التوصيل':
        return <Truck className="w-4 h-4" />;
      case 'قيد التجهيز':
        return <Clock className="w-4 h-4" />;
      case 'تم الإلغاء':
        return <XCircle className="w-4 h-4" />;
      default:
        return <Package className="w-4 h-4" />;
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center mb-6">
          <Button 
            variant="ghost" 
            onClick={() => navigate(-1)}
            className="mr-4"
          >
            <ArrowRight className="w-4 h-4 ml-2" />
            رجوع
          </Button>
          <h1 className="text-2xl font-bold">تفاصيل الطلب</h1>
        </div>
        <Card>
          <CardContent className="p-6">
            <Skeleton className="h-8 w-40 mb-4" />
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-3/4 mb-6" />
            <Skeleton className="h-32 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center mb-6">
          <Button 
            variant="ghost" 
            onClick={() => navigate(-1)}
            className="mr-4"
          >
            <ArrowRight className="w-4 h-4 ml-2" />
            رجوع
          </Button>
          <h1 className="text-2xl font-bold">تفاصيل الطلب</h1>
        </div>
        <Card>
          <CardContent className="p-6 text-center">
            <p className="text-red-500 mb-4">حدث خطأ أثناء تحميل تفاصيل الطلب</p>
            <Button onClick={() => navigate(-1)}>العودة للطلبات</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex items-center mb-6">
        <Button 
          variant="ghost" 
          onClick={() => navigate(-1)}
          className="mr-4"
        >
          <ArrowRight className="w-4 h-4 ml-2" />
          رجوع
        </Button>
        <h1 className="text-2xl font-bold">تفاصيل الطلب #{order.id}</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="border-blue-200 shadow-md">
          <CardHeader className="bg-gradient-to-r from-blue-50 to-blue-100 border-b border-blue-200">
            <CardTitle className="flex items-center gap-2 text-blue-700">
              <User className="w-5 h-5" />
              معلومات العميل
            </CardTitle>
          </CardHeader>
          <CardContent className="bg-blue-50/30">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <User className="w-4 h-4 text-gray-500" />
                <span className="font-medium">الاسم:</span>
                <span>{order.customer_name || "غير محدد"}</span>
              </div>
              <div className="flex items-center gap-2">
                <Phone className="w-4 h-4 text-gray-500" />
                <span className="font-medium">رقم الهاتف:</span>
                <span>{order.customer_phone || "غير محدد"}</span>
              </div>
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4 text-gray-500" />
                <span className="font-medium">العنوان:</span>
                <span>{order.customer_address || "غير محدد"}</span>
              </div>
              {order.customer_email && (
                <div className="flex items-center gap-2">
                  <Mail className="w-4 h-4 text-gray-500" />
                  <span className="font-medium">البريد الإلكتروني:</span>
                  <span>{order.customer_email}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="border-green-200 shadow-md">
          <CardHeader className="bg-gradient-to-r from-green-50 to-green-100 border-b border-green-200">
            <CardTitle className="flex items-center gap-2 text-green-700">
              <ShoppingCart className="w-5 h-5" />
              معلومات الطلب
            </CardTitle>
          </CardHeader>
          <CardContent className="bg-green-50/30">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4 text-gray-500" />
                <span className="font-medium">تاريخ الطلب:</span>
                <span>{new Date(order.created_date).toLocaleDateString('ar-EG', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}</span>
              </div>
              <div className="flex items-center gap-2">
                <Package className="w-4 h-4 text-gray-500" />
                <span className="font-medium">عدد المنتجات:</span>
                <span>{order.items_count || 0}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="font-medium">الإجمالي:</span>
                <span className="font-bold text-lg">{order.total_price || 0} ج</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="font-medium text-green-600">إجمالي العمولة:</span>
                <span className="font-bold text-lg text-green-600">
                  {orderItems.reduce((total, item) => total + (item.commission_amount || (item.price * 0.1)), 0).toFixed(2)} ج
                </span>
              </div>
              <div className="flex items-center gap-2">
                <span className="font-medium">الحالة:</span>
                <Badge className={getStatusColor(order.status)}>
                  {getStatusIcon(order.status)}
                  <span className="mr-1">{order.status}</span>
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="mt-6 border-purple-200 shadow-md">
        <CardHeader className="bg-gradient-to-r from-purple-50 to-purple-100 border-b border-purple-200">
          <CardTitle className="flex items-center gap-2 text-purple-700">
            <Package className="w-5 h-5" />
            المنتجات
          </CardTitle>
        </CardHeader>
        <CardContent className="bg-purple-50/30">
          {orderItems.length > 0 ? (
            <div className="space-y-4">
              {orderItems.map((item) => (
                <div key={item.id} className="flex items-center gap-4 p-4 border rounded-lg">
                  {item.product_image && (
                    <img 
                      src={item.product_image} 
                      alt={item.product_name} 
                      className="w-16 h-16 object-cover rounded-md"
                    />
                  )}
                  <div className="flex-1">
                    <h3 className="font-medium">{item.product_name}</h3>
                    <p className="text-sm text-gray-500">الكمية: {item.quantity}</p>
                    <p className="text-sm text-gray-500">السعر: {item.price} ج</p>
                    <p className="text-sm text-green-600 font-medium">عمولة المنتج: {item.commission_amount} ج</p>
                    {item.size && <p className="text-sm text-gray-500">المقاس: {item.size}</p>}
                    {item.color && <p className="text-sm text-gray-500">اللون: {item.color}</p>}
                  </div>
                  <div className="text-left">
                    <p className="font-bold">{(item.price * item.quantity).toFixed(2)} ج</p>
                  </div>
                </div>
              ))}
              
              <div className="bg-gradient-to-br from-amber-50 to-orange-50 p-4 rounded-xl border-2 border-amber-300 shadow-md mt-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="font-bold text-gray-700">إجمالي المنتجات:</span>
                  <span className="font-black text-xl text-gray-900">{orderItems.reduce((total, item) => total + item.quantity, 0)}</span>
                </div>
                <div className="flex justify-between items-center mb-2">
                  <span className="font-bold text-gray-700">إجمالي السعر:</span>
                  <span className="font-black text-2xl text-blue-600">{order.total_price || 0} ج</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="font-bold text-gray-700">إجمالي عمولتك:</span>
                  <span className="font-black text-2xl text-green-600 flex items-center gap-1">
                    <TrendingUp className="w-6 h-6" />
                    {orderItems.reduce((total, item) => total + (item.commission_amount * item.quantity), 0).toFixed(2)} ج
                  </span>
                </div>
              </div>
            </div>
          ) : (
            <p className="text-center py-4 text-gray-500">لا توجد منتجات في هذا الطلب</p>
          )}
        </CardContent>
      </Card>

      {order.notes && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="w-5 h-5" />
              ملاحظات الطلب
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700">{order.notes}</p>
          </CardContent>
        </Card>
      )}
      
      {order.status === 'تم التسليم' && (
        <Card className="mt-6 border-2 border-green-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <span className="text-lg font-bold text-green-800">عمولتك من هذا الطلب:</span>
              <span className="font-black text-xl text-green-600 flex items-center gap-1">
                <TrendingUp className="w-5 h-5" />
                {orderItems.reduce((total, item) => total + (item.commission_amount || (item.price * 0.1)), 0).toFixed(2)} ج
              </span>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="mt-4 mb-8 md:mb-4 flex justify-center">
        <a
          href={`https://wa.me/+201127642349?text=${encodeURIComponent(`طلب استعلام - كود الطلب: #${order.id}\nالعميل: ${order.customer_name}\nرقم الهاتف: ${order.customer_phone}\nالعنوان: ${order.customer_address}\nالمبلغ الإجمالي: ${order.total_price} ج\n\nتم إرسال الاستعلام بواسطة: ${localStorage.getItem("userName") || "المسوق"}`)}`}
          target="_blank"
          rel="noopener noreferrer"
          className="inline-flex items-center justify-center px-6 py-3 rounded-lg bg-green-500 hover:bg-green-600 text-white shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105 active:scale-95 font-medium"
        >
          تواصل معنا عبر واتساب
        </a>
      </div>
    </div>
  );
}
