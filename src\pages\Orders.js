import React, { useState, useEffect, useMemo } from "react";
import base44 from "../api/base44Client";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import { createPageUrl } from "../utils";
import { Card, CardContent, CardHeader, CardTitle } from "../components/ui/Card";
import { Button } from "../components/ui/Button";
import { Badge } from "../components/ui/Badge";
import { Input } from "../components/ui/Input";
import {
  ShoppingCart,
  Package,
  TrendingUp,
  Eye,
  CheckCircle,
  Clock,
  Truck,
  XCircle,
  Search,
  Users,
  AlertCircle,
  CheckSquare,
  ArrowRightCircle
} from "lucide-react";
import { Skeleton } from "../components/ui/Skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../components/ui/Tabs";

export default function Orders() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [user, setUser] = useState(null);
  const [query, setQuery] = useState("");

  const [activeTab, setActiveTab] = useState("all");
  const [filterStatus, setFilterStatus] = useState("الكل");

  useEffect(() => {
    loadUser();
  }, []);

  const loadUser = async () => {
    try {
      const currentUser = await base44.auth.me();
      setUser(currentUser);
    } catch (error) {
      console.error("Error loading user:", error);
    }
  };

  const { data: orders = [], isLoading } = useQuery({
    queryKey: ['user-orders'],
    queryFn: () => base44.entities.Order.filter({ marketer_email: user?.email }, '-created_date'),
    enabled: !!user?.email,
  });

  // بيانات تجريبية للطلبات
  const sampleOrders = useMemo(() => [
    {
      id: 1,
      customer_name: "أحمد محمد",
      customer_phone: "01234567890",
      address: "شارع النور، حي السلام، مدينة نصر",
      governorate: "القاهرة",
      status: "مكتمل",
      created_date: "2023-10-15T10:30:00Z",
      commission_amount: 50.00,
    },
    {
      id: 2,
      customer_name: "فاطمة علي",
      customer_phone: "01098765432",
      address: "شارع الأزهر، حي الجامعة، الإسكندرية",
      governorate: "الإسكندرية",
      status: "قيد المعالجة",
      created_date: "2023-10-14T14:20:00Z",
      commission_amount: 75.50,
    },
    {
      id: 3,
      customer_name: "محمد أحمد",
      customer_phone: "01123456789",
      address: "شارع التحرير، حي وسط البلد، الإسكندرية",
      governorate: "الإسكندرية",
      status: "جديد",
      created_date: "2023-10-13T09:15:00Z",
      commission_amount: 30.00,
    },
    {
      id: 4,
      customer_name: "سارة عبدالله",
      customer_phone: "01234567891",
      address: "شارع 26 يوليو، حي النخيل، الجيزة",
      governorate: "الجيزة",
      status: "مرفوض",
      created_date: "2023-10-12T16:45:00Z",
      commission_amount: 0.00,
    },
    {
      id: 5,
      customer_name: "علي حسن",
      customer_phone: "01234567892",
      address: "شارع النيل، حي فيصل، القاهرة",
      governorate: "القاهرة",
      status: "مكتمل",
      created_date: "2023-10-11T11:30:00Z",
      commission_amount: 120.00,
    },
    {
      id: 6,
      customer_name: "نورا يوسف",
      customer_phone: "01123456780",
      address: "شارع الجامعة، حي الدقي، القاهرة",
      governorate: "القاهرة",
      status: "جديد",
      created_date: "2023-10-10T13:20:00Z",
      commission_amount: 65.75,
    },
    {
      id: 7,
      customer_name: "خالد سالم",
      customer_phone: "01098765433",
      address: "شارع الشاطئ، حي السيدي بشر، الإسكندرية",
      governorate: "الإسكندرية",
      status: "قيد المعالجة",
      created_date: "2023-10-09T15:10:00Z",
      commission_amount: 45.00,
    },
    {
      id: 8,
      customer_name: "مريم حسن",
      customer_phone: "01234567893",
      address: "شارع القاهرة، حي المعادي، القاهرة",
      governorate: "القاهرة",
      status: "مكتمل",
      created_date: "2023-10-08T10:45:00Z",
      commission_amount: 90.25,
    },
    {
      id: 9,
      customer_name: "يوسف أحمد",
      customer_phone: "01123456781",
      address: "شارع المنصورة، حي المعمورة، الجيزة",
      governorate: "الجيزة",
      status: "جديد",
      created_date: "2023-10-07T14:30:00Z",
      commission_amount: 55.50,
    },
    {
      id: 10,
      customer_name: "فاطمة سعيد",
      customer_phone: "01098765434",
      address: "شارع 6 أكتوبر، حي المهندسين، الجيزة",
      governorate: "الجيزة",
      status: "قيد المعالجة",
      created_date: "2023-10-06T09:15:00Z",
      commission_amount: 80.00,
    },
  ], []);

  // جلب منتجات كل طلب بشكل منفصل
  const { data: allOrderItems = [] } = useQuery({
    queryKey: ['all-order-items', orders],
    queryFn: async () => {
      if (!orders || orders.length === 0) return [];
      const orderIds = orders.map(order => order.id);
      // جلب المنتجات لكل طلب على حدة
      const itemsPromises = orderIds.map(orderId =>
        base44.entities.OrderItem.filter({ order_id: orderId })
      );
      const itemsArrays = await Promise.all(itemsPromises);
      // دمج كل المصفوفات في مصفوفة واحدة
      return itemsArrays.flat();
    },
    enabled: !!user?.email && orders && orders.length > 0,
  });

  const getStatusIcon = (status) => {
    switch (status) {
      case 'تم التسليم':
        return <CheckCircle className="w-4 h-4" />;
      case 'قيد الانتظار':
        return <Clock className="w-4 h-4" />;
      case 'جارٍ التجهيز':
        return <AlertCircle className="w-4 h-4" />;
      case 'جارٍ التوصيل':
      case 'قيد الشحن':
        return <Truck className="w-4 h-4" />;
      case 'إلغاء':
      case 'ملغي':
        return <XCircle className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'تم التسليم':
        return 'bg-green-100 text-green-800';
      case 'قيد الانتظار':
        return 'bg-purple-100 text-purple-800';
      case 'جارٍ التجهيز':
        return 'bg-orange-100 text-orange-800';
      case 'جارٍ التوصيل':
      case 'قيد الشحن':
        return 'bg-cyan-100 text-cyan-800';
      case 'إلغاء':
      case 'ملغي':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // دالة لتحديث حالة الطلب لعرضها بشكل متوافق مع الفلاتر
  const normalizeStatus = (status) => {
    switch (status) {
      case 'قيد المعالجة':
        return 'قيد الانتظار';
      case 'قيد التجهيز':
        return 'جارٍ التجهيز';
      case 'قيد الشحن':
        return 'جارٍ التوصيل';
      case 'ملغي':
        return 'إلغاء';
      default:
        return status;
    }
  };

  const filteredOrders = useMemo(() => {
    const q = query.trim().toLowerCase();
    return orders.filter((order) => {
      const matchesSearch = !q ||
        (order.customer_name && order.customer_name.toLowerCase().includes(q)) ||
        (order.customer_phone && order.customer_phone.includes(q)) ||
        (order.id && order.id.toString().includes(q));
      const matchesStatus = filterStatus === "الكل" || normalizeStatus(order.status) === filterStatus;
      return matchesSearch && matchesStatus;
    });
  }, [orders, query, filterStatus]);

  const totalOrders = orders.length;
  const completedOrders = orders.filter(order => order.status === 'تم التسليم').length;
  const cancelledOrders = orders.filter(order => order.status === 'ملغي' || order.status === 'إلغاء').length;
  const pendingOrders = orders.filter(order => order.status === 'قيد الانتظار' || order.status === 'قيد المعالجة').length;
  const preparingOrders = orders.filter(order => order.status === 'جارٍ التجهيز' || order.status === 'قيد التجهيز').length;
  const deliveringOrders = orders.filter(order => order.status === 'جارٍ التوصيل' || order.status === 'قيد الشحن').length;
  const totalCommission = orders
    .filter(order => order.status === 'تم التسليم')
    .reduce((sum, order) => {
      // حساب العمولة من المنتجات في الطلب
      const orderItems = allOrderItems.filter(item => item.order_id === order.id);
      const orderCommission = orderItems.length > 0 ?
        orderItems.reduce((total, item) => total + (item.commission_amount * item.quantity), 0) :
        (order.commission_amount || 0);
      return sum + orderCommission;
    }, 0);

  const statusOptions = ["الكل", "قيد الانتظار", "جارٍ التجهيز", "جارٍ التوصيل", "تم التسليم", "إلغاء"];

  return (
    <div className="min-h-screen p-4 overflow-x-hidden">

      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-14 h-14 bg-black rounded-2xl flex items-center justify-center shadow-2xl border-2 border-yellow-400">
            <ShoppingCart className="w-8 h-8 text-yellow-400" />
          </div>
          <div>
            <h1 className="text-4xl font-black text-gray-900 mb-2" style={{ fontFamily: 'Cairo, sans-serif' }}>طلباتي</h1>
            <p className="text-gray-600 font-medium text-lg">تتبع طلباتك الحالية والسابقة</p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
        <Card className="border-2 border-blue-200 shadow-xl bg-gradient-to-br from-blue-50 to-indigo-100">
          <CardContent className="p-4 text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg">
              <ShoppingCart className="w-8 h-8 text-white" />
            </div>
            <h3 className="font-black text-2xl text-gray-900">{totalOrders}</h3>
            <p className="text-sm text-blue-700 font-bold">إجمالي الطلبات</p>
          </CardContent>
        </Card>

        <Card className="border-2 border-green-200 shadow-xl bg-gradient-to-br from-green-50 to-emerald-100">
          <CardContent className="p-4 text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg">
              <CheckCircle className="w-8 h-8 text-white" />
            </div>
            <h3 className="font-black text-2xl text-gray-900">{completedOrders}</h3>
            <p className="text-sm text-green-700 font-bold">تم التسليم</p>
          </CardContent>
        </Card>



        <Card className="border-2 border-purple-200 shadow-xl bg-gradient-to-br from-purple-50 to-pink-100">
          <CardContent className="p-4 text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg">
              <Clock className="w-8 h-8 text-white" />
            </div>
            <h3 className="font-black text-2xl text-gray-900">{pendingOrders}</h3>
            <p className="text-sm text-purple-700 font-bold">قيد الانتظار</p>
          </CardContent>
        </Card>

        <Card className="border-2 border-orange-200 shadow-xl bg-gradient-to-br from-orange-50 to-red-100">
          <CardContent className="p-4 text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg">
              <AlertCircle className="w-8 h-8 text-white" />
            </div>
            <h3 className="font-black text-2xl text-gray-900">{preparingOrders}</h3>
            <p className="text-sm text-orange-700 font-bold">جارٍ التجهيز</p>
          </CardContent>
        </Card>

        <Card className="border-2 border-cyan-200 shadow-xl bg-gradient-to-br from-cyan-50 to-blue-100">
          <CardContent className="p-4 text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg">
              <Truck className="w-8 h-8 text-white" />
            </div>
            <h3 className="font-black text-2xl text-gray-900">{deliveringOrders}</h3>
            <p className="text-sm text-cyan-700 font-bold">جارٍ التوصيل</p>
          </CardContent>
        </Card>

        <Card className="border-2 border-red-200 shadow-xl bg-gradient-to-br from-red-50 to-pink-100">
          <CardContent className="p-4 text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg">
              <XCircle className="w-8 h-8 text-white" />
            </div>
            <h3 className="font-black text-2xl text-gray-900">{cancelledOrders}</h3>
            <p className="text-sm text-red-700 font-bold">إلغاء</p>
          </CardContent>
        </Card>
      </div>

      {/* Filter */}
      <Card className="mt-12 mb-6 border-2 border-indigo-500 shadow-xl bg-gradient-to-r from-indigo-800 to-purple-800 text-white">
        <CardContent className="pt-8 pb-4 px-8">
          <div className="flex flex-wrap gap-2 justify-center">
            {statusOptions.map((status) => (
              <Button
                key={status}
                variant={filterStatus === status ? "default" : "outline"}
                size="sm"
                onClick={() => setFilterStatus(status)}
                className={
                  filterStatus === status
                    ? "bg-gradient-to-r from-yellow-400 to-orange-500 text-black shadow-lg font-bold transform hover:scale-105 transition-transform"
                    : "bg-white text-indigo-700 hover:bg-indigo-100 border-2 border-indigo-300 font-bold"
                }
              >
                {status}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Orders List */}
      <Card className="border-2 border-gray-300 shadow-xl bg-gray-100">
        <CardHeader className="pb-3 bg-gradient-to-r from-indigo-800 to-purple-800 border-b border-indigo-600">
          <CardTitle className="text-xl font-bold text-yellow-400 mb-3" style={{ fontFamily: 'Cairo, sans-serif' }}>
            {filterStatus === "الكل" ? "جميع الطلبات" : `الطلبات (${filterStatus})`}
          </CardTitle>

          {/* شريط البحث */}
          <div className="relative mt-4">
            <Search className="absolute right-3 top-3 w-5 h-5 text-teal-500" />
            <Input
              placeholder="البحث باسم العميل أو رقم الهاتف..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              className="pl-10 pr-10 py-2.5 sm:py-3 border-2 border-teal-200 rounded-lg focus:border-teal-500 focus:outline-none bg-white shadow-sm text-sm sm:text-base"
            />
            {query && (
              <button
                onClick={() => setQuery("")}
                className="absolute left-3 top-2.5 sm:top-3 text-gray-500 hover:text-gray-700 text-sm sm:text-base"
                aria-label="مسح البحث"
              >
                ✖
              </button>
            )}
          </div>
        </CardHeader>

        <CardContent className="p-0">
          {isLoading ? (
            <div className="p-8">
              <Skeleton className="h-64 w-full" />
            </div>
          ) : filteredOrders.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500">لا توجد طلبات مطابقة</p>
            </div>
          ) : (
            <div className="p-4 space-y-4 overflow-x-hidden">
              {filteredOrders.map((order) => {
                const orderItems = allOrderItems.filter(item => item.order_id === order.id);
                const commissionAmount = orderItems.length > 0
                  ? orderItems.reduce((total, item) => total + (item.commission_amount * item.quantity), 0)
                  : (order.commission_amount || 0);
                const totalPrice = orderItems.reduce((total, item) => total + (item.price * item.quantity), 0);

                return (
                  <Card key={order.id} className="border-2 border-indigo-200 shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden w-full">
                    <div className="bg-gradient-to-r from-indigo-800 to-purple-800 text-white p-4">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center gap-2">
                          <ShoppingCart className="w-5 h-5 text-yellow-400" />
                          <span className="font-bold text-lg">طلب #{order.id}</span>
                        </div>
                        <Badge className={getStatusColor(order.status)}>
                          <div className="flex items-center gap-1 justify-center">
                            {getStatusIcon(order.status)}
                            <span>{order.status}</span>
                          </div>
                        </Badge>
                      </div>
                    </div>

                    <div className="p-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <Users className="w-4 h-4 text-indigo-600" />
                            <span className="text-sm font-semibold text-gray-700">العميل:</span>
                            <span className="font-medium">{order.customer_name}</span>
                          </div>

                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-semibold text-gray-700">رقم الهاتف:</span>
                            <span className="font-medium">{order.customer_phone}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-semibold text-gray-700">التاريخ:</span>
                            <span className="font-medium">{new Date(order.created_date).toLocaleDateString("ar-EG", { year: "numeric", month: "2-digit", day: "2-digit" })}</span>
                            <Button
                              size="sm"
                              onClick={() => navigate(`/orders/${order.id}`)}
                              className="bg-blue-600 hover:bg-blue-700 text-white mr-auto"
                            >
                              <Eye className="w-4 h-4 ml-1" />
                              عرض التفاصيل
                            </Button>
                          </div>

                        </div>
                      </div>


                    </div>
                  </Card>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}