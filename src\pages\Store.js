import React, { useState, useEffect } from "react";
import base44 from "../api/base44Client";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useNavigate, useLocation } from "react-router-dom";
import { createPageUrl } from "../utils";
import { Card, CardContent } from "../components/ui/Card";
import { Badge } from "../components/ui/Badge";
import { Button } from "../components/ui/Button";
import { useToast } from "../components/ui/Toast";
import {
  Trophy,
  TrendingUp,
  Package,
  Star,
  ShoppingBag,
  Shirt,
  Watch,
  Footprints,
  Heart,
  ShoppingCart,
  Search,
  Filter,
  ChevronRight
} from "lucide-react";
import { Skeleton } from "../components/ui/Skeleton";

const categoryIcons = {
  "رجالي": Shirt,
  "حريمي": ShoppingBag,
  "أحذية": Footprints,
  "شنط": Package,
  "إكسسوارات": Watch
};

export default function Store() {
  const navigate = useNavigate();
  const location = useLocation();
  const queryClient = useQueryClient();
  const [user, setUser] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState("الكل");
  const [searchQuery, setSearchQuery] = useState("");

  const [currentPage, setCurrentPage] = useState(1);
  const productsPerPage = 20; // عدد المنتجات في كل صفحة

  useEffect(() => {
    loadUser();
    const urlParams = new URLSearchParams(window.location.search);
    const search = urlParams.get('search');
    setSearchQuery(search || '');
  }, []);
  
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const search = urlParams.get('search');
    setSearchQuery(search || '');
  }, [location.search]);

  const loadUser = async () => {
    try {
      const currentUser = await base44.auth.me();
      setUser(currentUser);
    } catch (error) {
      console.error("Error loading user:", error);
    }
  };

  // بيانات وهمية للمنتجات
  const mockProducts = [
    // منتجات رجالية
    { id: 1, name: "تيشيرت رجالي قطن", price: 120, commission_amount: 20, category: "رجالي", image_url: "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400" },
    { id: 2, name: "بنطال جينز رجالي", price: 250, commission_amount: 40, category: "رجالي", image_url: "https://images.unsplash.com/photo-1542272604-787c3835535d?w=400" },
    { id: 3, name: "جاكيت جلد رجالي", price: 800, commission_amount: 120, category: "رجالي", image_url: "https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400" },
    { id: 4, name: "قميص رسمي رجالي", price: 200, commission_amount: 30, category: "رجالي", image_url: "https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=400" },
    { id: 5, name: "حذاء رياضي رجالي", price: 350, commission_amount: 50, category: "رجالي", image_url: "https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400" },
    { id: 6, name: "ساعة يد رجالية", price: 500, commission_amount: 80, category: "إكسسوارات", image_url: "https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400" },
    
    // منتجات حريمية
    { id: 7, name: "فستان صيفي حريمي", price: 300, commission_amount: 45, category: "حريمي", image_url: "https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400" },
    { id: 8, name: "بلوزة حريرية حريمية", price: 280, commission_amount: 40, category: "حريمي", image_url: "https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400" },
    { id: 9, name: "تنورة قصيرة حريمية", price: 180, commission_amount: 25, category: "حريمي", image_url: "https://images.unsplash.com/photo-1539109136881-3be0616acf4b?w=400" },
    { id: 10, name: "حقيبة يد حريمية", price: 450, commission_amount: 70, category: "شنط", image_url: "https://images.unsplash.com/photo-1584917865442-de6df1ccb87d?w=400" },
    { id: 11, name: "حذاء كعب عالي", price: 400, commission_amount: 60, category: "أحذية", image_url: "https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400" },
    { id: 12, name: "طقم مجوهرات", price: 600, commission_amount: 90, category: "إكسسوارات", image_url: "https://images.unsplash.com/photo-1599643448515-bc3c849cb17f?w=400" },
    
    // المزيد من المنتجات الرجالية
    { id: 13, name: "سويت شيرت رجالي", price: 220, commission_amount: 35, category: "رجالي", image_url: "https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400" },
    { id: 14, name: "حزام جلد رجالي", price: 150, commission_amount: 25, category: "إكسسوارات", image_url: "https://images.unsplash.com/photo-1620799140408-edc6dcb6d633?w=400" },
    { id: 15, name: "نظارات شمسية رجالية", price: 280, commission_amount: 45, category: "إكسسوارات", image_url: "https://images.unsplash.com/photo-1473496169904-658ba7c44d8a?w=400" },
    { id: 16, name: "حذاء رسمي رجالي", price: 450, commission_amount: 70, category: "أحذية", image_url: "https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400" },
    { id: 17, name: "محفظة جلد رجالية", price: 200, commission_amount: 30, category: "إكسسوارات", image_url: "https://images.unsplash.com/photo-1627123424554-4176b5c64c27?w=400" },
    { id: 18, name: "تيشيرت بولو رجالي", price: 160, commission_amount: 25, category: "رجالي", image_url: "https://images.unsplash.com/photo-1620799140408-edc6dcb6d633?w=400" },
    
    // المزيد من المنتجات الحريمية
    { id: 19, name: "جاكيت حريمي", price: 500, commission_amount: 80, category: "حريمي", image_url: "https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400" },
    { id: 20, name: "بنطال جينز حريمي", price: 280, commission_amount: 45, category: "حريمي", image_url: "https://images.unsplash.com/photo-1542272604-787c3835535d?w=400" },
    { id: 21, name: "بلوزة صوف حريمية", price: 350, commission_amount: 55, category: "حريمي", image_url: "https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400" },
    { id: 22, name: "حقيبة ظهر حريمية", price: 380, commission_amount: 60, category: "شنط", image_url: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400" },
    { id: 23, name: "صندل حريمي", price: 200, commission_amount: 30, category: "أحذية", image_url: "https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400" },
    { id: 24, name: "عقد فضي حريمي", price: 320, commission_amount: 50, category: "إكسسوارات", image_url: "https://images.unsplash.com/photo-1599643448515-bc3c849cb17f?w=400" },
    
    // منتجات أحذية متنوعة
    { id: 25, name: "حذاء كوتشي", price: 650, commission_amount: 100, category: "أحذية", image_url: "https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400" },
    { id: 26, name: "حذاء رياضي نسائي", price: 420, commission_amount: 65, category: "أحذية", image_url: "https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400" },
    { id: 27, name: "حذاء جلد شرابي", price: 550, commission_amount: 85, category: "أحذية", image_url: "https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400" },
    { id: 28, name: "جزمة نسائية صيفية", price: 280, commission_amount: 45, category: "أحذية", image_url: "https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400" },
    { id: 29, name: "حذاء بوت رجالي", price: 700, commission_amount: 110, category: "أحذية", image_url: "https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400" },
    { id: 30, name: "صندل رجالي", price: 250, commission_amount: 40, category: "أحذية", image_url: "https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400" },
    
    // منتجات شنط متنوعة
    { id: 31, name: "حقيبة سفر", price: 800, commission_amount: 120, category: "شنط", image_url: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400" },
    { id: 32, name: "حقيبة كمبيوتر محمول", price: 450, commission_amount: 70, category: "شنط", image_url: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400" },
    { id: 33, name: "حقيبة كتف جلد", price: 550, commission_amount: 85, category: "شنط", image_url: "https://images.unsplash.com/photo-1584917865442-de6df1ccb87d?w=400" },
    { id: 34, name: "حقيبة يد نسائية", price: 380, commission_amount: 60, category: "شنط", image_url: "https://images.unsplash.com/photo-1584917865442-de6df1ccb87d?w=400" },
    { id: 35, name: "حقيبة ظهر جلد", price: 650, commission_amount: 100, category: "شنط", image_url: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400" },
    { id: 36, name: "حقيبة سفر صغيرة", price: 320, commission_amount: 50, category: "شنط", image_url: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400" }
  ];

  // استخدام البيانات الوهمية مباشرة بدلاً من جلب البيانات من الخادم
  const products = mockProducts;
  const productsLoading = false;



  const { data: favorites = [] } = useQuery({
    queryKey: ['favorites'],
    queryFn: () => base44.entities.Favorite.filter({ marketer_email: user?.email }),
    enabled: !!user?.email,
  });

  const addToFavoritesMutation = useMutation({
    mutationFn: async (product) => {
      return await base44.entities.Favorite.create({
        product_id: product.id,
        product_name: product.name,
        product_price: product.price,
        product_image: product.image_url,
        commission_amount: product.commission_amount,
        marketer_id: user?.id,
        marketer_email: user?.email
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['favorites']);

      // التحقق من عدم وجود رسالة معروضة بالفعل
      const existingNotification = document.querySelector('.favorite-notification');
      if (existingNotification || window.favoriteNotificationVisible) {
        return;
      }

      // إظهار رسالة تأكيد جميلة
      const notification = document.createElement('div');
      notification.className = 'favorite-notification fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 flex items-center space-x-2 rtl:space-x-reverse animate-pulse';
      notification.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
        </svg>
        <span>تم إضافة المنتج إلى المفضلة بنجاح!</span>
      `;
      document.body.appendChild(notification);

      // إزالة الإشعار بعد 3 ثوانٍ
      setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transition = 'opacity 0.5s';
        setTimeout(() => {
          if (document.body.contains(notification)) {
            document.body.removeChild(notification);
          }
        }, 500);
      }, 3000);

      // إضافة متغير لتتبع حالة الرسالة
      window.favoriteNotificationVisible = true;

      // إعادة تعيين الحالة بعد اختفاء الرسالة
      setTimeout(() => {
        window.favoriteNotificationVisible = false;
      }, 3500);
    },
    onError: (error) => {
      console.error("خطأ في إضافة المنتج للمفضلة:", error);
      alert("حدث خطأ أثناء إضافة المنتج للمفضلة، يرجى المحاولة مرة أخرى.");
    },
  });

  const removeFromFavoritesMutation = useMutation({
    mutationFn: async (productId) => {
      const favorite = favorites.find(f => f.product_id === productId);
      if (favorite) {
        await base44.entities.Favorite.delete(favorite.id);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['favorites']);

      // التحقق من عدم وجود رسالة معروضة بالفعل
      const existingNotification = document.querySelector('.favorite-notification');
      if (existingNotification || window.favoriteNotificationVisible) {
        return;
      }

      // إظهار رسالة تأكيد جميلة
      const notification = document.createElement('div');
      notification.className = 'favorite-notification fixed top-4 right-4 bg-gray-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 flex items-center space-x-2 rtl:space-x-reverse animate-pulse';
      notification.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
        </svg>
        <span>تم إزالة المنتج من المفضلة!</span>
      `;
      document.body.appendChild(notification);

      // إزالة الإشعار بعد 3 ثوانٍ
      setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transition = 'opacity 0.5s';
        setTimeout(() => {
          if (document.body.contains(notification)) {
            document.body.removeChild(notification);
          }
        }, 500);
      }, 3000);

      // إضافة متغير لتتبع حالة الرسالة
      window.favoriteNotificationVisible = true;

      // إعادة تعيين الحالة بعد اختفاء الرسالة
      setTimeout(() => {
        window.favoriteNotificationVisible = false;
      }, 3500);
    },
    onError: (error) => {
      console.error("خطأ في إزالة المنتج من المفضلة:", error);
      alert("حدث خطأ أثناء إزالة المنتج من المفضلة، يرجى المحاولة مرة أخرى.");
    },
  });

  const addToCartMutation = useMutation({
    mutationFn: async (product) => {
      return await base44.entities.CartItem.create({
        product_id: product.id,
        product_name: product.name,
        product_price: product.price,
        product_image: product.image_url,
        commission_amount: product.commission_amount,
        quantity: 1,
        marketer_id: user?.id,
        marketer_email: user?.email
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['cart']);
      // التحقق من عدم وجود رسالة معروضة بالفعل
      const existingNotification = document.querySelector('.cart-notification');
      if (existingNotification || window.cartNotificationVisible) {
        return;
      }
      
      // إظهار رسالة تأكيد جميلة
      const notification = document.createElement('div');
      notification.className = 'cart-notification fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 flex items-center space-x-2 rtl:space-x-reverse animate-pulse';
      notification.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <span>تم إضافة المنتج إلى السلة بنجاح!</span>
      `;
      document.body.appendChild(notification);
      
      // إزالة الإشعار بعد 3 ثوانٍ
      setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transition = 'opacity 0.5s';
        setTimeout(() => {
          if (document.body.contains(notification)) {
            document.body.removeChild(notification);
          }
        }, 500);
      }, 3000);
      
      // إضافة متغير لتتبع حالة الرسالة
      window.cartNotificationVisible = true;
      
      // إعادة تعيين الحالة بعد اختفاء الرسالة
      setTimeout(() => {
        window.cartNotificationVisible = false;
      }, 3500);
    },
    onError: (error) => {
      console.error("خطأ في إضافة المنتج للسلة:", error);
      alert("حدث خطأ أثناء إضافة المنتج للسلة، يرجى المحاولة مرة أخرى.");
    },
  });

  const isFavorite = (productId) => {
    return favorites.some(f => f.product_id === productId);
  };

  // حفظ حالة المفضلة في التخزين المحلي
  useEffect(() => {
    const favoriteIds = favorites.map(f => f.product_id);
    localStorage.setItem('favoriteProducts', JSON.stringify(favoriteIds));
  }, [favorites]);

  // استرجاع حالة المفضلة من التخزين المحلي عند تحميل الصفحة
  useEffect(() => {
    const savedFavorites = localStorage.getItem('favoriteProducts');
    if (savedFavorites) {
      const favoriteIds = JSON.parse(savedFavorites);
      // التأكد من أن المنتجات في المفضلة تتطابق مع المنتجات المحفوظة
      favoriteIds.forEach(id => {
        if (!isFavorite(id) && products.find(p => p.id === id)) {
          // إذا كان المنتج محفوظاً في التخزين المحلي ولكن ليس في قائمة المفضلة، أضفه
          const product = products.find(p => p.id === id);
          if (product) {
            addToFavoritesMutation.mutate(product);
          }
        }
      });
    }
  }, [products]);

  const toggleFavorite = (e, product) => {
    e.stopPropagation();
    if (isFavorite(product.id)) {
      removeFromFavoritesMutation.mutate(product.id);
    } else {
      addToFavoritesMutation.mutate(product);
    }
  };

  const handleAddToCart = (e, product) => {
    e.stopPropagation();
    
    // التحقق من عدم وجود عملية إضافة قيد التنفيذ
    if (addToCartMutation.isLoading) {
      return;
    }
    
    // التحقق من عدم وجود رسالة معروضة بالفعل
    if (window.cartNotificationVisible) {
      return;
    }
    
    addToCartMutation.mutate(product);
  };

  const { data: orders = [], isLoading: ordersLoading } = useQuery({
    queryKey: ['all-orders'],
    queryFn: () => base44.entities.Order.filter({ status: 'تم التسليم' }),
  });

  const getTopMarketers = () => {
    const marketerStats = {};
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    // بيانات وهمية للمسوقين
    const mockMarketers = [
      { email: "<EMAIL>", count: 28, total: 4200 },
      { email: "<EMAIL>", count: 25, total: 3750 },
      { email: "<EMAIL>", count: 22, total: 3300 },
      { email: "<EMAIL>", count: 20, total: 3000 },
      { email: "<EMAIL>", count: 18, total: 2700 },
      { email: "<EMAIL>", count: 15, total: 2250 },
      { email: "<EMAIL>", count: 13, total: 1950 },
      { email: "<EMAIL>", count: 11, total: 1650 },
      { email: "<EMAIL>", count: 9, total: 1350 },
      { email: "<EMAIL>", count: 7, total: 1050 }
    ];

    // إضافة البيانات الوهمية
    mockMarketers.forEach(marketer => {
      marketerStats[marketer.email] = {
        email: marketer.email,
        count: marketer.count,
        total: marketer.total
      };
    });

    // تحديث البيانات بالطلبات الحقيقية إن وجدت
    orders.forEach(order => {
      const orderDate = new Date(order.created_date);
      if (orderDate.getMonth() === currentMonth && orderDate.getFullYear() === currentYear) {
        if (!marketerStats[order.marketer_email]) {
          marketerStats[order.marketer_email] = {
            email: order.marketer_email,
            count: 0,
            total: 0
          };
        }
        marketerStats[order.marketer_email].count += 1;
        marketerStats[order.marketer_email].total += order.commission_amount || 0;
      }
    });

    return Object.values(marketerStats)
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  };

  const topMarketers = getTopMarketers();

  const filteredProducts = products.filter((product) => {
    const matchesSearch = !searchQuery || product.name?.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === "الكل" || product.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });
  
  // حساب المنتجات المعروضة في الصفحة الحالية
  const indexOfLastProduct = currentPage * productsPerPage;
  const indexOfFirstProduct = indexOfLastProduct - productsPerPage;
  const currentProducts = filteredProducts.slice(indexOfFirstProduct, indexOfLastProduct);
  
  // حساب عدد الصفحات
  const totalPages = Math.ceil(filteredProducts.length / productsPerPage);
  
  // تغيير الصفحة مع انتقال سلس
  const paginate = (pageNumber) => {
    // الانتقال لأعلى الصفحة أولاً
    window.scrollTo({ top: 0, behavior: 'smooth' });
    // ثم تغيير الصفحة
    setCurrentPage(pageNumber);
  };
  
  // إعادة تعيين الصفحة عند تغيير الفئة أو البحث
  useEffect(() => {
    setCurrentPage(1);
  }, [selectedCategory, searchQuery]);

  return (
    <div className="min-h-screen overflow-x-hidden bg-white">
      <style>{`
        @keyframes spin-slow {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }
        .animate-spin-slow {
          animation: spin-slow 8s linear infinite;
        }
        @keyframes scroll-left {
          0% {
            transform: translateX(100%);
          }
          100% {
            transform: translateX(-100%);
          }
        }
        .animate-scroll-right {
          animation: scroll-left 25s linear infinite;
          display: flex;
          width: max-content;
        }
      `}</style>

      {/* Hero Header */}
      <div className={`mx-4 my-6 relative h-56 rounded-3xl overflow-hidden shadow-2xl border-2 ${
false 
          ? ' border-gray-600' 
          : ' border-yellow-500'
      }`}>

        <img
          src="https://annabaa.org/aarticles/fileM/23/57c2ac274caad.jpg"
          alt="Sport Manufacturing"
          className="w-full h-full object-cover opacity-100"
        />


      </div>

      {/* Categories */}
      <div className={`mx-4 my-6 p-6 rounded-3xl shadow-2xl border-2 ${
false 
          ? 'bg-gradient-to-r from-gray-800 to-gray-700 border-gray-600' 
          : 'bg-gradient-to-r from-white to-gray-50 border-gray-200'
      }`}>
        <div className="flex items-center justify-between mb-6 max-w-4xl mx-auto">
          <div className={`p-4 rounded-xl shadow-lg transform transition-all duration-200 hover:scale-105 bg-gradient-to-r from-yellow-50 to-amber-50`}>
            <h3 className={`font-black text-lg text-gray-900`} style={{ fontFamily: 'Cairo, sans-serif' }}>تصفح حسب الفئة</h3>
          </div>
        </div>
        <div className="max-w-4xl mx-auto grid grid-cols-3 md:grid-cols-5 gap-4">
          <button
            onClick={() => setSelectedCategory("الكل")}
            className={`flex flex-col items-center gap-3 transform transition-all duration-200 ${
              selectedCategory === "الكل" ? 'scale-110' : 'hover:scale-105'
            }`}
          >
            <div className={`w-20 h-20 md:w-24 md:h-24 rounded-full flex items-center justify-center shadow-xl border-4 transform transition-all duration-200 ${
              selectedCategory === "الكل"
                ? 'bg-gradient-to-br from-black to-gray-800 border-yellow-400 hover:shadow-2xl'
                : 'bg-gradient-to-br from-gray-100 to-gray-200 border-gray-300 hover:shadow-xl'
            }`}>
              <Package className={`w-10 h-10 md:w-12 md:h-12 ${selectedCategory === "الكل" ? 'text-yellow-400' : 'text-gray-600'}`} />
            </div>
            <div className={`p-2 rounded-lg ${selectedCategory === "الكل" ? 'bg-yellow-400' : 'bg-gray-100'}`}>
              <span className={`text-sm md:text-base font-black ${
                selectedCategory === "الكل" ? 'text-black' : 'text-gray-600'
              }`} style={{ fontFamily: 'Cairo, sans-serif' }}>
                الكل
              </span>
            </div>
          </button>
          {Object.entries(categoryIcons).map(([category, Icon]) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`flex flex-col items-center gap-3 transform transition-all duration-200 ${
                selectedCategory === category ? 'scale-110' : 'hover:scale-105'
              }`}
            >
              <div className={`w-20 h-20 md:w-24 md:h-24 rounded-full flex items-center justify-center shadow-xl border-4 transform transition-all duration-200 ${
                selectedCategory === category
                  ? 'bg-gradient-to-br from-black to-gray-800 border-yellow-400 hover:shadow-2xl'
                  : 'bg-gradient-to-br from-gray-100 to-gray-200 border-gray-300 hover:shadow-xl'
              }`}>
                <Icon className={`w-10 h-10 md:w-12 md:h-12 ${selectedCategory === category ? 'text-yellow-400' : 'text-gray-600'}`} />
              </div>
              <div className={`p-2 rounded-lg ${selectedCategory === category ? 'bg-yellow-400' : 'bg-gray-100'}`}>
                <span className={`text-sm md:text-base font-black ${
                  selectedCategory === category ? 'text-black' : 'text-gray-600'
                }`} style={{ fontFamily: 'Cairo, sans-serif' }}>
                  {category}
                </span>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Top Marketers */}
      {topMarketers.length > 0 && (
        <div className={`mx-4 my-6 p-4 rounded-2xl shadow-xl border-2 bg-black border-gray-700 overflow-hidden`}>
          <div className="flex items-center justify-between mb-4">
            <div className={`p-4 rounded-xl shadow-lg transform transition-all duration-200 hover:scale-105 bg-gradient-to-r from-gray-900 to-gray-800`}>
              <div className="flex items-center gap-2">
                <Trophy className="w-6 h-6 text-yellow-400" />
                <h2 className="font-black text-lg text-yellow-400" style={{ fontFamily: 'Cairo, sans-serif' }}>أفضل المسوّقين هذا الشهر</h2>
              </div>
            </div>
          </div>
          
          <div className="flex flex-col items-center justify-center py-6 gap-6">
            {/* Top Marketer - First Place */}
            {topMarketers[0] && (
              <div className="flex flex-col items-center transform transition-all duration-200 hover:scale-105">
                <div className="relative">
                  <div className={`w-20 h-20 md:w-24 md:h-24 rounded-full flex items-center justify-center border-4 border-yellow-400 shadow-lg bg-gradient-to-br from-gray-700 to-gray-800`}>
                    <span className="text-white font-bold text-2xl md:text-3xl">
                      {topMarketers[0].email.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div className="absolute -top-1 -right-1 w-7 h-7 md:w-8 md:h-8 rounded-full bg-yellow-400 text-black flex items-center justify-center text-sm md:text-base font-bold animate-pulse">
                    {topMarketers[0].count}
                  </div>
                  <div className="absolute -bottom-1 -right-1 w-7 h-7 md:w-8 md:h-8 rounded-full bg-yellow-400 flex items-center justify-center">
                    <Trophy className="w-5 h-5 text-black" fill="black" />
                  </div>
                </div>
                <p className="mt-2 text-sm md:text-base text-white font-bold text-center" style={{ fontFamily: 'Cairo, sans-serif' }}>
                  {topMarketers[0].email.split('@')[0]}
                </p>
              </div>
            )}
            
            {/* Second Row - Four Marketers */}
            <div className="flex gap-4 md:gap-8 justify-center">
              {topMarketers.slice(1, 5).map((marketer, index) => (
                <div key={marketer.email} className="flex flex-col items-center transform transition-all duration-200 hover:scale-105">
                  <div className="relative">
                    <div className={`w-14 h-14 md:w-16 md:h-16 rounded-full flex items-center justify-center border-2 ${
                      index < 2 ? 'border-gray-400' : 'border-orange-400'
                    } shadow-lg bg-gradient-to-br from-gray-700 to-gray-800`}>
                      <span className="text-white font-bold text-lg md:text-xl">
                        {marketer.email.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div className={`absolute -top-1 -right-1 w-5 h-5 md:w-6 md:h-6 rounded-full ${
                      index < 2 ? 'bg-gray-400' : 'bg-orange-400'
                    } text-white flex items-center justify-center text-xs font-bold animate-pulse`}>
                      {marketer.count}
                    </div>
                    {index < 2 && (
                      <div className="absolute -bottom-1 -right-1 w-5 h-5 md:w-6 md:h-6 rounded-full bg-gray-400 flex items-center justify-center">
                        <Star className="w-3 h-3 text-white" fill="white" />
                      </div>
                    )}
                  </div>
                  <p className="mt-2 text-sm text-white font-bold text-center" style={{ fontFamily: 'Cairo, sans-serif' }}>
                    {marketer.email.split('@')[0]}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Products Grid */}
      <div className={`mx-1 my-6 p-6 rounded-3xl shadow-2xl border-2 w-full overflow-hidden ${
false 
          ? 'bg-gradient-to-r from-gray-900 to-gray-800 border-gray-600' 
          : 'bg-gradient-to-r from-yellow-100 to-yellow-50 border-yellow-200'
      }`}>
          <div className="mb-6 flex items-center justify-between">
            <div className={`inline-block p-4 rounded-xl shadow-lg transform transition-all duration-200 hover:scale-105 bg-gradient-to-r from-yellow-50 to-amber-50`}>
              <h3 className={`font-black text-lg text-gray-900`} style={{ fontFamily: 'Cairo, sans-serif' }}>المنتجات المتاحة</h3>
            </div>
            <div className={`p-4 rounded-xl shadow-lg transform transition-all duration-200 hover:scale-105 bg-gradient-to-r from-white to-gray-50`}>
              <span className={`text-base font-bold text-blue-600`} style={{ fontFamily: 'Cairo, sans-serif' }}>{currentProducts.length} منتج</span>
            </div>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 md:gap-6 px-1 py-2">
            {productsLoading ? (
              Array.from({ length: 8 }).map((_, i) => (
                <div key={i} className="space-y-3">
                  <Skeleton className="h-48 w-full rounded-2xl" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              ))
            ) : filteredProducts.length === 0 ? (
              <div className="col-span-full text-center py-12">
                <Package className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-black text-gray-700 mb-2" style={{ fontFamily: 'Cairo, sans-serif' }}>لا توجد منتجات</h3>
                <p className="text-gray-500 font-semibold">لم يتم العثور على منتجات تطابق بحثك</p>
              </div>
            ) : (
              currentProducts.map((product) => (
                <Card
                  key={product.id}
                  className={`overflow-hidden border-2 border-yellow-200 bg-white shadow-xl hover:shadow-2xl transition-all hover:scale-105 cursor-pointer rounded-2xl`}
                  onClick={() => navigate(`/product/${product.id}`)}
                  style={{ padding: 0, margin: 0, border: 'none' }}
                >
                  <div className={`h-48 relative overflow-hidden p-0 m-0`} style={{ padding: 0, margin: 0, border: 'none' }}>
                    {product.image_url ? (
                      <img
                        src={product.image_url}
                        alt={product.name}
                        className="w-full h-full object-cover rounded-none"
                        style={{ margin: 0, padding: 0, border: 'none', display: 'block', boxSizing: 'border-box' }}
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <Package className="w-12 h-12 text-yellow-300" />
                      </div>
                    )}
                    <button
                      onClick={(e) => toggleFavorite(e, product)}
                      className={`absolute top-3 left-3 flex items-center justify-center transition-all bg-white rounded-full p-2 shadow-md ${
                        isFavorite(product.id)
                          ? 'text-red-500'
                          : 'text-gray-400 hover:text-red-500'
                      }`}
                    >
                      <Heart className={`w-6 h-6 ${isFavorite(product.id) ? 'fill-current' : ''}`} />
                    </button>
                    {product.category && (
                      <Badge className="absolute top-3 right-3 bg-yellow-400 text-black font-bold">
                        {product.category}
                      </Badge>
                    )}
                  </div>
                  <CardContent className="p-4">
                    <div className={`p-4 rounded-xl mb-3 shadow-md transform transition-all duration-200 hover:scale-102 bg-gradient-to-r from-gray-50 to-white`}>
                      <h3 className={`font-black text-sm md:text-lg text-gray-900`} style={{ fontFamily: 'Cairo, sans-serif' }}>
                        {product.name}
                      </h3>
                    </div>
                    <div className="flex flex-col gap-2 mb-4">
                      <div className={`flex items-center justify-between p-3 rounded-lg shadow-md transform transition-all duration-200 hover:scale-102 bg-gradient-to-r from-blue-50 to-blue-100`}>
                        <span className={`text-sm font-bold text-gray-600`} style={{ fontFamily: 'Cairo, sans-serif' }}>السعر</span>
                        <span className={`text-lg font-bold text-blue-600`}>{product.price}ج</span>
                      </div>
                      <div className={`flex items-center justify-between p-3 rounded-lg shadow-md transform transition-all duration-200 hover:scale-102 bg-gradient-to-r from-green-50 to-green-100`}>
                        <span className={`text-sm font-bold text-gray-600`} style={{ fontFamily: 'Cairo, sans-serif' }}>العمولة</span>
                        <span className={`text-lg font-bold text-green-600`}>{product.commission_amount}ج</span>
                      </div>
                    </div>
                    <button
                      onClick={(e) => handleAddToCart(e, product)}
                      disabled={addToCartMutation.isLoading}
                      className={`w-full py-3 px-4 rounded-lg font-bold transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl flex items-center justify-center gap-2 ${
                        addToCartMutation.isLoading
                          ? 'opacity-50 cursor-not-allowed'
                          : ''
                      } ${
                false 
                          ? 'bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-300 hover:to-yellow-400 text-gray-900' 
                          : 'bg-gradient-to-r from-black to-gray-800 hover:from-gray-800 hover:to-gray-700 text-yellow-400'
                      }`}
                      style={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      {addToCartMutation.isLoading ? (
                        <>
                          <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                          <span className="whitespace-nowrap">جاري الإضافة...</span>
                        </>
                      ) : (
                        <>
                          <ShoppingCart className="w-5 h-5" />
                          <span className="whitespace-nowrap">أضف</span>
                        </>
                      )}
                    </button>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
          
          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center items-center mt-8 space-x-2 space-x-reverse">
              <button
                onClick={() => paginate(currentPage > 1 ? currentPage - 1 : 1)}
                disabled={currentPage === 1}
                className={`px-4 py-2 rounded-lg font-bold transition-all ${
                  currentPage === 1
                    ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                    : 'bg-yellow-100 text-gray-700 hover:bg-yellow-200'
                }`}
                style={{ fontFamily: 'Cairo, sans-serif' }}
              >
                السابق
              </button>
              
              {/* Page Numbers */}
              {Array.from({ length: totalPages }, (_, i) => i + 1).map((number) => (
                <button
                  key={number}
                  onClick={() => paginate(number)}
                  className={`px-4 py-2 rounded-lg font-bold transition-all ${
                    currentPage === number
                      ? 'bg-gray-800 text-yellow-400'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                  style={{ fontFamily: 'Cairo, sans-serif' }}
                >
                  {number}
                </button>
              ))}
              
              <button
                onClick={() => paginate(currentPage < totalPages ? currentPage + 1 : totalPages)}
                disabled={currentPage === totalPages}
                className={`px-4 py-2 rounded-lg font-bold transition-all ${
                  currentPage === totalPages
                    ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                    : 'bg-yellow-100 text-gray-700 hover:bg-yellow-200'
                }`}
                style={{ fontFamily: 'Cairo, sans-serif' }}
              >
                التالي
              </button>
            </div>
          )}
      </div>
    </div>
  );
}
