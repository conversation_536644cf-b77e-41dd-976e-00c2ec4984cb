/* تحسينات للأجهزة القديمة بدءًا من عام 2010 */

/* تحسينات للأجهزة الصغيرة جداً (iPhone 4/4S) */
@media (max-width: 320px) {
  body {
    font-size: 12px;
  }

  .container {
    padding: 8px;
  }

  h1, h2, h3 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }

  .btn, button {
    padding: 6px 12px;
    font-size: 0.75rem;
  }

  .card {
    margin-bottom: 8px;
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 8px;
  }

  .logo {
    width: 32px;
    height: 32px;
  }

  .search-bar {
    width: 100%;
  }

  .nav-item {
    padding: 6px;
    font-size: 0.75rem;
  }

  .product-card {
    width: 100%;
    height: auto;
  }

  .product-image {
    height: 120px;
  }

  .product-title {
    font-size: 0.75rem;
    line-height: 1.2;
  }

  .price, .commission {
    font-size: 0.875rem;
  }
}

/* تحسينات للأجهزة الصغيرة (iPhone 5/5S/SE) */
@media (max-width: 375px) {
  .container {
    padding: 12px;
  }

  h1, h2, h3 {
    font-size: 1.1rem;
  }

  .btn, button {
    padding: 8px 14px;
    font-size: 0.875rem;
  }

  .logo {
    width: 36px;
    height: 36px;
  }

  .nav-item {
    padding: 8px;
    font-size: 0.875rem;
  }

  .product-image {
    height: 140px;
  }

  .product-title {
    font-size: 0.875rem;
  }

  .price, .commission {
    font-size: 1rem;
  }
}

/* تحسينات للأجهزة متوسطة الحجم (iPhone 6/6S/7/8) */
@media (max-width: 414px) {
  .container {
    padding: 14px;
  }

  h1, h2, h3 {
    font-size: 1.2rem;
  }

  .btn, button {
    padding: 10px 16px;
    font-size: 0.9rem;
  }

  .logo {
    width: 40px;
    height: 40px;
  }

  .nav-item {
    padding: 10px;
    font-size: 0.9rem;
  }

  .product-image {
    height: 160px;
  }

  .product-title {
    font-size: 0.9rem;
  }

  .price, .commission {
    font-size: 1.1rem;
  }
}

/* تحسينات للأجهزة متوسطة الحجم (iPhone 6 Plus/7 Plus/8 Plus) */
@media (max-width: 736px) {
  .container {
    padding: 16px;
  }

  h1, h2, h3 {
    font-size: 1.3rem;
  }

  .btn, button {
    padding: 12px 18px;
    font-size: 1rem;
  }

  .logo {
    width: 44px;
    height: 44px;
  }

  .nav-item {
    padding: 12px;
    font-size: 1rem;
  }

  .product-image {
    height: 180px;
  }

  .product-title {
    font-size: 1rem;
  }

  .price, .commission {
    font-size: 1.2rem;
  }
}

/* تحسينات للأجهزة الكبيرة (iPhone X/XR/11/12/13/14/15) */
@media (min-width: 375px) and (max-width: 428px) {
  .container {
    padding: 18px;
  }

  h1, h2, h3 {
    font-size: 1.4rem;
  }

  .btn, button {
    padding: 14px 20px;
    font-size: 1.1rem;
  }

  .logo {
    width: 48px;
    height: 48px;
  }

  .nav-item {
    padding: 14px;
    font-size: 1.1rem;
  }

  .product-image {
    height: 200px;
  }

  .product-title {
    font-size: 1.1rem;
  }

  .price, .commission {
    font-size: 1.3rem;
  }
}

/* تحسينات للأجهزة اللوحية (iPad) */
@media (min-width: 768px) and (max-width: 1024px) {
  .container {
    padding: 20px;
  }

  h1, h2, h3 {
    font-size: 1.6rem;
  }

  .btn, button {
    padding: 16px 22px;
    font-size: 1.2rem;
  }

  .logo {
    width: 52px;
    height: 52px;
  }

  .nav-item {
    padding: 16px;
    font-size: 1.2rem;
  }

  .product-image {
    height: 220px;
  }

  .product-title {
    font-size: 1.2rem;
  }

  .price, .commission {
    font-size: 1.4rem;
  }

  .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

/* تحسينات للأجهزة اللوحية الكبيرة (iPad Pro) */
@media (min-width: 1024px) {
  .container {
    padding: 24px;
  }

  h1, h2, h3 {
    font-size: 1.8rem;
  }

  .btn, button {
    padding: 18px 24px;
    font-size: 1.3rem;
  }

  .logo {
    width: 56px;
    height: 56px;
  }

  .nav-item {
    padding: 18px;
    font-size: 1.3rem;
  }

  .product-image {
    height: 240px;
  }

  .product-title {
    font-size: 1.3rem;
  }

  .price, .commission {
    font-size: 1.5rem;
  }

  .grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

/* تحسينات للأجهزة اللوحية الكبيرة جداً */
@media (min-width: 1280px) {
  .grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
}

/* تحسينات عامة */
* {
  box-sizing: border-box;
}

body {
  overflow-x: hidden;
}

img {
  max-width: 100%;
  height: auto;
}

button {
  cursor: pointer;
  touch-action: manipulation;
}

input, textarea, select {
  font-size: 16px; /* منع التكبير في iOS */
}

/* تحسينات للأجهزة التي تعمل باللمس */
@media (hover: none) {
  button:hover {
    background-color: inherit;
  }

  a:hover {
    color: inherit;
  }
}

/* تحسينات للأجهزة القديمة ذات الذاكرة المحدودة */
* {
  -webkit-transform: translateZ(0);
  -webkit-backface-visibility: hidden;
  -webkit-perspective: 1000;
}

/* تحسينات للأجهزة القديمة */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  select,
  textarea,
  input[type="text"],
  input[type="password"],
  input[type="datetime"],
  input[type="datetime-local"],
  input[type="date"],
  input[type="month"],
  input[type="time"],
  input[type="week"],
  input[type="number"],
  input[type="email"],
  input[type="url"],
  input[type="search"],
  input[type="tel"],
  input[type="color"] {
    font-size: 16px;
  }
}

/* تحسينات للأجهزة القديمة التي لا تدعم flexbox */
.flex-container {
  display: block;
  text-align: center;
}

.flex-container > * {
  display: inline-block;
  vertical-align: top;
  text-align: right;
}

/* تحسينات للأجهزة القديمة التي لا تدعم grid */
.grid-container {
  display: block;
}

.grid-container > * {
  display: inline-block;
  width: 48%;
  margin: 1%;
  vertical-align: top;
}

/* تحسينات للأجهزة القديمة التي لا تدعم position: fixed */
@media (max-width: 767px) {
  .fixed-header,
  .fixed-footer {
    position: relative;
  }
}

/* تحسينات للأجهزة القديمة التي لا تدعم box-shadow */
.no-boxshadow .card,
.no-boxshadow .btn,
.no-boxshadow button {
  border: 1px solid #ccc;
}
